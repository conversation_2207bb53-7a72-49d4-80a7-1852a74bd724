import React, { useEffect, useRef } from 'react';
import { Card, List, Tag, Typography } from 'antd';
import { 
  InfoCircleOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  CloseCircleOutlined,
  FileTextOutlined 
} from '@ant-design/icons';

const { Text } = Typography;

const LogViewer = ({ logs }) => {
  const listRef = useRef(null);

  useEffect(() => {
    // 自动滚动到最新日志
    if (listRef.current) {
      listRef.current.scrollTop = listRef.current.scrollHeight;
    }
  }, [logs]);

  const getLogIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getLogColor = (type) => {
    switch (type) {
      case 'success':
        return 'green';
      case 'error':
        return 'red';
      case 'warning':
        return 'orange';
      default:
        return 'blue';
    }
  };

  return (
    <Card 
      title={
        <span>
          <FileTextOutlined /> Operation Log
        </span>
      }
      size="small"
      className="log-card"
    >
      <div 
        ref={listRef}
        style={{ 
          height: '400px', 
          overflowY: 'auto',
          padding: '8px 0'
        }}
      >
        <List
          size="small"
          dataSource={logs}
          renderItem={(log) => (
            <List.Item 
              key={log.id}
              style={{ 
                padding: '8px 12px',
                borderBottom: '1px solid #f0f0f0',
                alignItems: 'flex-start'
              }}
            >
              <div style={{ width: '100%' }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  marginBottom: '4px' 
                }}>
                  {getLogIcon(log.type)}
                  <Tag 
                    color={getLogColor(log.type)} 
                    size="small" 
                    style={{ marginLeft: '8px', marginRight: '8px' }}
                  >
                    {log.type.toUpperCase()}
                  </Tag>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    {log.timestamp}
                  </Text>
                </div>
                <Text style={{ fontSize: '13px', lineHeight: '1.4' }}>
                  {log.message}
                </Text>
              </div>
            </List.Item>
          )}
        />
        {logs.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            color: '#999'
          }}>
            No logs yet. Start exploration to see real-time updates.
          </div>
        )}
      </div>
    </Card>
  );
};

export default LogViewer;
