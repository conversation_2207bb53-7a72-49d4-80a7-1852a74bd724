body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

.app-container {
  min-height: 100vh;
  padding: 20px;
}

.screenshot-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.screenshot-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.log-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.log-entry {
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
  background: #f6f8fa;
}

.log-entry.error {
  border-left-color: #ff4d4f;
  background: #fff2f0;
}

.log-entry.success {
  border-left-color: #52c41a;
  background: #f6ffed;
}

.log-entry.warning {
  border-left-color: #faad14;
  background: #fffbe6;
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.running {
  background-color: #52c41a;
  animation: pulse 2s infinite;
}

.status-indicator.stopped {
  background-color: #ff4d4f;
}

.status-indicator.idle {
  background-color: #faad14;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
