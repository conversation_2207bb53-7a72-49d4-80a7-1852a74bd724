import React from 'react';
import { Card, Statistic, Row, Col, Progress, Tag, Space } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  CheckCircleOutlined,
  MobileOutlined,
  WifiOutlined,
  RobotOutlined
} from '@ant-design/icons';

const StatusPanel = ({ status, isConnected, isInitialized }) => {
  const getStatusColor = () => {
    if (!isConnected) return 'red';
    if (status.is_running) return 'green';
    if (status.task_complete) return 'blue';
    return 'orange';
  };

  const getStatusText = () => {
    if (!isConnected) return 'Disconnected';
    if (status.is_running) return 'Running';
    if (status.task_complete) return 'Completed';
    if (isInitialized) return 'Ready';
    return 'Not Initialized';
  };

  const getStatusIcon = () => {
    if (!isConnected) return <WifiOutlined />;
    if (status.is_running) return <PlayCircleOutlined />;
    if (status.task_complete) return <CheckCircleOutlined />;
    return <PauseCircleOutlined />;
  };

  return (
    <Card title="System Status" size="small" style={{ marginBottom: 16 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <Tag 
            color={getStatusColor()} 
            icon={getStatusIcon()}
            style={{ fontSize: '14px', padding: '4px 12px' }}
          >
            {getStatusText()}
          </Tag>
        </div>
        
        <Row gutter={16}>
          <Col span={12}>
            <Statistic
              title="Current Round"
              value={status.current_round}
              prefix={<RobotOutlined />}
              valueStyle={{ fontSize: '18px' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="Clients"
              value={status.connected_clients}
              prefix={<WifiOutlined />}
              valueStyle={{ fontSize: '18px' }}
            />
          </Col>
        </Row>
        
        {status.task_description && (
          <div style={{ marginTop: 12 }}>
            <div style={{ 
              fontSize: '12px', 
              color: '#666', 
              marginBottom: '4px' 
            }}>
              Current Task:
            </div>
            <div style={{ 
              fontSize: '13px', 
              padding: '8px', 
              background: '#f5f5f5', 
              borderRadius: '4px',
              lineHeight: '1.4'
            }}>
              {status.task_description}
            </div>
          </div>
        )}
        
        <div style={{ marginTop: 12 }}>
          <div style={{ 
            fontSize: '12px', 
            color: '#666', 
            marginBottom: '8px' 
          }}>
            System Components:
          </div>
          <Space wrap>
            <Tag color={isConnected ? 'green' : 'red'}>
              WebSocket
            </Tag>
            <Tag color={status.has_controller ? 'green' : 'red'}>
              Device Controller
            </Tag>
            <Tag color={isInitialized ? 'green' : 'red'}>
              AI Model
            </Tag>
          </Space>
        </div>
      </Space>
    </Card>
  );
};

export default StatusPanel;
