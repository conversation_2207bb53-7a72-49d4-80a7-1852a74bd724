#!/usr/bin/env python3
"""
简化版实时探索脚本 - 专门用于服务器调用
支持WebSocket实时数据传输和检测模式选择
"""

import argparse
import json
import sys
import time
import os

def emit_realtime_data(data_type, data):
    """发送实时数据到服务器"""
    output_data = {
        'type': data_type,
        'data': data
    }
    print(json.dumps(output_data), flush=True)

def run_exploration(app_name, task_desc, detection_mode):
    """运行探索的主函数"""
    
    # 发送启动消息
    emit_realtime_data("log", {
        "message": f"🚀 Starting exploration for app: {app_name}",
        "level": "info"
    })
    
    emit_realtime_data("log", {
        "message": f"📋 Task: {task_desc}",
        "level": "info"
    })
    
    emit_realtime_data("log", {
        "message": f"🔍 Detection mode: {detection_mode}",
        "level": "info"
    })
    
    # 模拟探索过程
    for step in range(1, 6):  # 模拟5步探索
        emit_realtime_data("log", {
            "message": f"=== Step {step} ===",
            "level": "info"
        })
        
        # 模拟截图
        emit_realtime_data("screenshot", {
            "step_number": step,
            "image_path": f"step_{step}.png",
            "image_base64": None
        })
        
        # 模拟操作
        emit_realtime_data("action", {
            "step_number": step,
            "action_type": f"click_element_{step}",
            "description": f"Clicking on element {step}"
        })
        
        # 模拟UX评估
        if step % 2 == 0:  # 每两步进行一次UX评估
            ux_data = {
                "type": "interface_usability",
                "score": 7.5 + step * 0.3,
                "step_number": step,
                "data": {
                    "detailed_analysis": {
                        "overall_score": 7.5 + step * 0.3,
                        "summary": f"Step {step} interface analysis",
                        "strengths": ["Good layout", "Clear navigation"],
                        "weaknesses": ["Small buttons", "Low contrast"],
                        "recommendations": ["Increase button size", "Improve color contrast"]
                    }
                }
            }
            emit_realtime_data("ux_evaluation", ux_data)
        
        # 等待一秒模拟真实探索
        time.sleep(1)
    
    # 发送完成消息
    emit_realtime_data("task_complete", {
        "message": "Task completed successfully!",
        "total_steps": 5,
        "detection_mode": detection_mode
    })
    
    emit_realtime_data("log", {
        "message": "✅ Exploration session finished",
        "level": "success"
    })

def main():
    """主函数 - 处理命令行参数并启动探索"""
    parser = argparse.ArgumentParser(description='AppAgent Realtime Explorer (Simple)')
    parser.add_argument('--app', type=str, required=True, help='Target app name')
    parser.add_argument('--task', type=str, help='Task description')
    parser.add_argument('--detection_mode', type=str, choices=['xml', 'visual'], 
                       default='xml', help='Detection mode (xml or visual)')
    parser.add_argument('--socket_mode', type=str, choices=['true', 'false'], 
                       default='false', help='Enable socket mode for real-time communication')
    
    args = parser.parse_args()
    
    # 如果没有提供任务描述，使用默认任务
    if not args.task:
        task_desc = f"Explore and test the {args.app} app functionality"
    else:
        task_desc = args.task
    
    # 启动探索
    try:
        run_exploration(args.app, task_desc, args.detection_mode)
    except Exception as e:
        emit_realtime_data("log", {
            "message": f"❌ Error during exploration: {str(e)}",
            "level": "error"
        })
        sys.exit(1)

if __name__ == "__main__":
    main()
