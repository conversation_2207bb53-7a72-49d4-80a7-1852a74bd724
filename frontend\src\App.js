import React, { useState, useEffect, useRef } from 'react';
import { Layout, Card, Button, Input, Select, Row, Col, Typography, Space, Tag, Progress, Alert } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined, MobileOutlined } from '@ant-design/icons';
import axios from 'axios';
import ScreenshotViewer from './components/ScreenshotViewer';
import LogViewer from './components/LogViewer';
import StatusPanel from './components/StatusPanel';
import UXReportViewer from './components/UXReportViewer';
import './App.css';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

const API_BASE_URL = 'http://localhost:8000';
const WS_URL = 'ws://localhost:8000/ws';

function App() {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [explorerStatus, setExplorerStatus] = useState({
    is_running: false,
    current_round: 0,
    task_description: '',
    task_complete: false,
    has_controller: false,
    connected_clients: 0
  });
  
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [appName, setAppName] = useState('');
  const [detectionMode, setDetectionMode] = useState('visual');
  const [isInitialized, setIsInitialized] = useState(false);
  
  const [currentScreenshot, setCurrentScreenshot] = useState(null);
  const [logs, setLogs] = useState([]);
  const [uxReport, setUxReport] = useState(null);
  
  const socketRef = useRef(null);

  useEffect(() => {
    // 获取设备列表
    fetchDevices();
    
    // 建立WebSocket连接
    connectWebSocket();
    
    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
    };
  }, []);

  const connectWebSocket = () => {
    // 使用原生WebSocket而不是socket.io
    const ws = new WebSocket(WS_URL);
    socketRef.current = ws;
    setSocket(ws);

    ws.onopen = () => {
      setIsConnected(true);
      addLog('Connected to server', 'success');
    };

    ws.onclose = () => {
      setIsConnected(false);
      addLog('Disconnected from server', 'error');
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      addLog(`WebSocket error: ${error}`, 'error');
    };
  };

  const handleWebSocketMessage = (message) => {
    const { type, data } = message;
    
    switch (type) {
      case 'status_update':
        setExplorerStatus(data);
        break;
        
      case 'screenshot_update':
        setCurrentScreenshot(data);
        addLog(`Screenshot updated - Round ${data.round} (${data.stage})`, 'info');
        break;
        
      case 'step_start':
        addLog(`Starting round ${data.round}`, 'info');
        break;
        
      case 'detection_result':
        addLog(`${data.mode} mode: Found ${data.elements_found} elements`, 'info');
        break;
        
      case 'ai_thinking':
        addLog(data.message, 'info');
        break;
        
      case 'ai_decision':
        addLog(`AI decided: ${data.action}`, 'success');
        break;
        
      case 'action_start':
        addLog(`Executing: ${data.action} - ${data.description}`, 'info');
        break;
        
      case 'action_executed':
        addLog(`Action completed: ${data.action}`, 'success');
        break;
        
      case 'task_complete':
        addLog('Task completed successfully!', 'success');
        setExplorerStatus(prev => ({ ...prev, task_complete: true, is_running: false }));
        break;
        
      case 'exploration_stopped':
        addLog('Exploration stopped', 'warning');
        setExplorerStatus(prev => ({ ...prev, is_running: false }));
        break;
        
      case 'report_generation_start':
        addLog('Generating UX evaluation report...', 'info');
        break;
        
      case 'report_generated':
        addLog('UX evaluation report generated', 'success');
        setUxReport(data);
        break;
        
      case 'error':
        addLog(`Error: ${message.message}`, 'error');
        break;
        
      default:
        console.log('Unknown message type:', type, data);
    }
  };

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { 
      id: Date.now(), 
      message, 
      type, 
      timestamp 
    }].slice(-100)); // 保持最新100条日志
  };

  const fetchDevices = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/devices`);
      setDevices(response.data.devices);
      if (response.data.devices.length > 0) {
        setSelectedDevice(response.data.devices[0]);
      }
    } catch (error) {
      addLog(`Failed to fetch devices: ${error.message}`, 'error');
    }
  };

  const initializeExplorer = async () => {
    if (!taskDescription || !appName) {
      addLog('Please fill in task description and app name', 'error');
      return;
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/initialize`, {
        task_description: taskDescription,
        app_name: appName,
        detection_mode: detectionMode
      });
      
      if (response.data.success) {
        setIsInitialized(true);
        addLog('Explorer initialized successfully', 'success');
        addLog(`Device: ${response.data.device}`, 'info');
        addLog(`Screen size: ${response.data.screen_size}`, 'info');
        addLog(`Detection mode: ${response.data.detection_mode}`, 'info');
      }
    } catch (error) {
      addLog(`Initialization failed: ${error.response?.data?.detail || error.message}`, 'error');
    }
  };

  const startExploration = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/start`);
      if (response.data.success) {
        addLog('Exploration started', 'success');
        setExplorerStatus(prev => ({ ...prev, is_running: true }));
      }
    } catch (error) {
      addLog(`Failed to start exploration: ${error.response?.data?.detail || error.message}`, 'error');
    }
  };

  const stopExploration = async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/stop`);
      if (response.data.success) {
        addLog('Exploration stopped', 'warning');
        setExplorerStatus(prev => ({ ...prev, is_running: false }));
      }
    } catch (error) {
      addLog(`Failed to stop exploration: ${error.response?.data?.detail || error.message}`, 'error');
    }
  };

  return (
    <Layout className="app-layout">
      <Header className="app-header">
        <div className="header-content">
          <Title level={3} style={{ color: 'white', margin: 0 }}>
            <MobileOutlined /> AppAgent Real-time Explorer
          </Title>
          <Space>
            <Tag color={isConnected ? 'green' : 'red'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Tag>
            <Tag color={explorerStatus.is_running ? 'blue' : 'default'}>
              {explorerStatus.is_running ? 'Running' : 'Idle'}
            </Tag>
          </Space>
        </div>
      </Header>
      
      <Layout>
        <Sider width={350} className="app-sider">
          <div className="sider-content">
            <StatusPanel 
              status={explorerStatus}
              isConnected={isConnected}
              isInitialized={isInitialized}
            />
            
            <Card title="Configuration" size="small" style={{ marginBottom: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>App Name:</Text>
                  <Input 
                    value={appName}
                    onChange={(e) => setAppName(e.target.value)}
                    placeholder="Enter app name"
                    disabled={isInitialized}
                  />
                </div>
                
                <div>
                  <Text strong>Task Description:</Text>
                  <Input.TextArea 
                    value={taskDescription}
                    onChange={(e) => setTaskDescription(e.target.value)}
                    placeholder="Describe the task you want the AI to perform"
                    rows={3}
                    disabled={isInitialized}
                  />
                </div>
                
                <div>
                  <Text strong>Detection Mode:</Text>
                  <Select 
                    value={detectionMode}
                    onChange={setDetectionMode}
                    style={{ width: '100%' }}
                    disabled={isInitialized}
                  >
                    <Option value="visual">Visual (YOLO)</Option>
                    <Option value="xml">XML</Option>
                  </Select>
                </div>
              </Space>
            </Card>
            
            <Card title="Controls" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                {!isInitialized ? (
                  <Button 
                    type="primary" 
                    onClick={initializeExplorer}
                    disabled={!taskDescription || !appName}
                    block
                  >
                    Initialize Explorer
                  </Button>
                ) : (
                  <Space style={{ width: '100%' }}>
                    <Button 
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={startExploration}
                      disabled={explorerStatus.is_running}
                    >
                      Start
                    </Button>
                    <Button 
                      danger
                      icon={<PauseCircleOutlined />}
                      onClick={stopExploration}
                      disabled={!explorerStatus.is_running}
                    >
                      Stop
                    </Button>
                    <Button 
                      icon={<ReloadOutlined />}
                      onClick={() => window.location.reload()}
                    >
                      Reset
                    </Button>
                  </Space>
                )}
              </Space>
            </Card>
          </div>
        </Sider>
        
        <Content className="app-content">
          <Row gutter={[16, 16]}>
            <Col span={16}>
              <ScreenshotViewer screenshot={currentScreenshot} />
            </Col>
            <Col span={8}>
              <LogViewer logs={logs} />
            </Col>
          </Row>
          
          {uxReport && (
            <Row style={{ marginTop: 16 }}>
              <Col span={24}>
                <UXReportViewer report={uxReport} />
              </Col>
            </Row>
          )}
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
