#!/usr/bin/env python3
"""
AppAgent实时探索系统启动脚本
自动启动后端API服务和前端开发服务器
"""

import os
import sys
import subprocess
import time
import threading
import signal
from pathlib import Path

def print_colored(text, color):
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, '')}{text}{colors['end']}")

def check_dependencies():
    """检查依赖项"""
    print_colored("🔍 Checking dependencies...", "blue")
    
    # 检查Python依赖
    try:
        import fastapi
        import uvicorn
        import websockets
        print_colored("✅ Python dependencies OK", "green")
    except ImportError as e:
        print_colored(f"❌ Missing Python dependency: {e}", "red")
        print_colored("Please run: pip install -r requirements.txt", "yellow")
        return False
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print_colored(f"✅ Node.js {result.stdout.strip()}", "green")
        else:
            raise FileNotFoundError
    except FileNotFoundError:
        print_colored("❌ Node.js not found", "red")
        print_colored("Please install Node.js from https://nodejs.org/", "yellow")
        return False
    
    return True

def install_frontend_dependencies():
    """安装前端依赖"""
    frontend_dir = Path(__file__).parent / "frontend"
    if not (frontend_dir / "node_modules").exists():
        print_colored("📦 Installing frontend dependencies...", "blue")
        try:
            subprocess.run(['npm', 'install'], cwd=frontend_dir, check=True)
            print_colored("✅ Frontend dependencies installed", "green")
        except subprocess.CalledProcessError:
            print_colored("❌ Failed to install frontend dependencies", "red")
            return False
    else:
        print_colored("✅ Frontend dependencies already installed", "green")
    return True

def start_backend():
    """启动后端服务"""
    print_colored("🚀 Starting backend server...", "blue")
    backend_dir = Path(__file__).parent / "backend"
    
    try:
        # 启动FastAPI服务器
        process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn', 'app:app', 
            '--host', '0.0.0.0', 
            '--port', '8000',
            '--reload'
        ], cwd=backend_dir)
        
        print_colored("✅ Backend server started on http://localhost:8000", "green")
        return process
    except Exception as e:
        print_colored(f"❌ Failed to start backend: {e}", "red")
        return None

def start_frontend():
    """启动前端服务"""
    print_colored("🚀 Starting frontend server...", "blue")
    frontend_dir = Path(__file__).parent / "frontend"
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['BROWSER'] = 'none'  # 防止自动打开浏览器
        
        process = subprocess.Popen([
            'npm', 'start'
        ], cwd=frontend_dir, env=env)
        
        print_colored("✅ Frontend server started on http://localhost:3000", "green")
        return process
    except Exception as e:
        print_colored(f"❌ Failed to start frontend: {e}", "red")
        return None

def wait_for_services():
    """等待服务启动"""
    print_colored("⏳ Waiting for services to start...", "yellow")
    time.sleep(5)
    
    # 检查后端服务
    try:
        import requests
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print_colored("✅ Backend service is ready", "green")
        else:
            print_colored("⚠️  Backend service may not be ready", "yellow")
    except Exception:
        print_colored("⚠️  Backend service may not be ready", "yellow")

def print_usage_info():
    """打印使用说明"""
    print_colored("\n" + "="*60, "cyan")
    print_colored("🎉 AppAgent Real-time Explorer System Started!", "green")
    print_colored("="*60, "cyan")
    print_colored("\n📋 Service URLs:", "blue")
    print_colored("   • Frontend:  http://localhost:3000", "white")
    print_colored("   • Backend:   http://localhost:8000", "white")
    print_colored("   • API Docs:  http://localhost:8000/docs", "white")
    
    print_colored("\n🔧 Usage Instructions:", "blue")
    print_colored("   1. Open http://localhost:3000 in your browser", "white")
    print_colored("   2. Connect your Android device via ADB", "white")
    print_colored("   3. Fill in the app name and task description", "white")
    print_colored("   4. Click 'Initialize Explorer' to set up", "white")
    print_colored("   5. Click 'Start' to begin autonomous exploration", "white")
    print_colored("   6. Watch real-time screenshots and logs", "white")
    print_colored("   7. View UX evaluation report when complete", "white")
    
    print_colored("\n⚠️  Prerequisites:", "yellow")
    print_colored("   • Android device connected via ADB", "white")
    print_colored("   • YOLO service running on port 7861 (optional)", "white")
    print_colored("   • AI model API keys configured in config.yaml", "white")
    
    print_colored("\n🛑 To stop the system:", "red")
    print_colored("   Press Ctrl+C in this terminal", "white")
    print_colored("\n" + "="*60, "cyan")

def signal_handler(signum, frame):
    """处理中断信号"""
    print_colored("\n🛑 Shutting down system...", "yellow")
    sys.exit(0)

def main():
    """主函数"""
    print_colored("🚀 AppAgent Real-time Explorer System", "purple")
    print_colored("Starting up...\n", "purple")
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 安装前端依赖
    if not install_frontend_dependencies():
        sys.exit(1)
    
    processes = []
    
    try:
        # 启动后端
        backend_process = start_backend()
        if backend_process:
            processes.append(backend_process)
        else:
            sys.exit(1)
        
        # 等待后端启动
        time.sleep(3)
        
        # 启动前端
        frontend_process = start_frontend()
        if frontend_process:
            processes.append(frontend_process)
        else:
            # 如果前端启动失败，终止后端
            backend_process.terminate()
            sys.exit(1)
        
        # 等待服务就绪
        wait_for_services()
        
        # 打印使用说明
        print_usage_info()
        
        # 等待进程
        while True:
            time.sleep(1)
            # 检查进程是否还在运行
            for process in processes:
                if process.poll() is not None:
                    print_colored(f"⚠️  Process {process.pid} has stopped", "yellow")
                    
    except KeyboardInterrupt:
        print_colored("\n🛑 Received interrupt signal", "yellow")
    except Exception as e:
        print_colored(f"❌ Unexpected error: {e}", "red")
    finally:
        # 清理进程
        print_colored("🧹 Cleaning up processes...", "yellow")
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            except Exception:
                pass
        
        print_colored("✅ System shutdown complete", "green")

if __name__ == "__main__":
    main()
