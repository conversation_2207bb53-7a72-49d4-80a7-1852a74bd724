.app-layout {
  min-height: 100vh;
}

.app-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.app-sider {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.sider-content {
  padding: 16px;
  height: 100%;
}

.app-content {
  padding: 16px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.screenshot-card {
  height: 100%;
  min-height: 500px;
}

.screenshot-card .ant-card-body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 57px);
}

.screenshot-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  min-height: 400px;
}

.screenshot-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.screenshot-image:hover {
  transform: scale(1.02);
}

.log-card {
  height: 100%;
}

.log-card .ant-card-body {
  padding: 8px;
  height: calc(100% - 57px);
}

.status-panel {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

.status-indicator.connected {
  background-color: #52c41a;
}

.status-indicator.disconnected {
  background-color: #ff4d4f;
}

.status-indicator.running {
  background-color: #1890ff;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.control-panel {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-entry {
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  background: #f6f8fa;
  transition: all 0.3s ease;
}

.log-entry:hover {
  background: #e6f7ff;
  transform: translateX(2px);
}

.log-entry.success {
  border-left-color: #52c41a;
  background: #f6ffed;
}

.log-entry.success:hover {
  background: #d9f7be;
}

.log-entry.error {
  border-left-color: #ff4d4f;
  background: #fff2f0;
}

.log-entry.error:hover {
  background: #ffccc7;
}

.log-entry.warning {
  border-left-color: #faad14;
  background: #fffbe6;
}

.log-entry.warning:hover {
  background: #fff1b8;
}

.ux-report-card {
  margin-top: 16px;
}

.score-card {
  text-align: center;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae7ff;
  border-radius: 8px;
  padding: 16px;
}

.score-card.excellent {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border-color: #b7eb8f;
}

.score-card.good {
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
  border-color: #ffe58f;
}

.score-card.poor {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
  border-color: #ffa39e;
}

.radar-chart-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.issue-item {
  padding: 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  margin-bottom: 8px;
}

.recommendation-item {
  padding: 12px;
  background: #f6ffed;
  border: 1px solid #d9f7be;
  border-radius: 6px;
  margin-bottom: 8px;
}

.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
  padding: 16px 0;
}

.ant-statistic-content-value {
  font-weight: 600;
}

.ant-progress-line {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .app-content {
    padding: 12px;
  }
  
  .sider-content {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .app-layout {
    flex-direction: column;
  }
  
  .app-sider {
    width: 100% !important;
    max-width: 100% !important;
    flex: none;
  }
  
  .header-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .screenshot-container {
    min-height: 300px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
