# AppAgent 实时探索系统

基于 `self_explore.py` 逻辑构建的前后端联通实时系统，提供AI驱动的移动应用自动化探索和UX评估。

## 🌟 系统特性

### 实时功能
- **实时截图传输**: WebSocket实时传输设备截图，保持原始比例
- **实时操作反馈**: 显示AI决策过程和操作执行状态
- **实时日志监控**: 查看详细的操作日志和系统状态
- **实时进度跟踪**: 监控探索进度和当前轮次

### 核心功能
- **AI自动探索**: 基于大语言模型的智能移动应用探索
- **多模态检测**: 支持视觉(YOLO)和XML两种UI元素检测模式
- **UX评估**: 自动生成专业的用户体验评估报告
- **可视化报告**: 交互式UX评估结果展示

## 🏗️ 系统架构

```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   React前端     │ ←──────────────→ │  FastAPI后端    │
│                 │                 │                 │
│ • 实时截图显示   │                 │ • 设备控制      │
│ • 操作日志      │                 │ • AI模型交互    │
│ • 状态监控      │                 │ • UX评估       │
│ • UX报告可视化  │                 │ • WebSocket服务 │
└─────────────────┘                 └─────────────────┘
                                            │
                                            ▼
                                    ┌─────────────────┐
                                    │  Android设备    │
                                    │                 │
                                    │ • ADB连接       │
                                    │ • 截图捕获      │
                                    │ • 操作执行      │
                                    └─────────────────┘
```

## 🚀 快速开始

### 1. 环境准备

**Python环境**:
```bash
# 安装Python依赖
pip install -r requirements.txt
```

**Node.js环境**:
```bash
# 确保安装了Node.js (推荐v16+)
node --version
npm --version
```

**Android设备**:
```bash
# 确保ADB可用并连接设备
adb devices
```

### 2. 配置设置

编辑 `config.yaml` 文件：
```yaml
MODEL: "Qwen"  # 或 "OpenAI"
DASHSCOPE_API_KEY: "your-api-key"  # Qwen API密钥
# 或
OPENAI_API_KEY: "your-api-key"     # OpenAI API密钥
```

### 3. 启动系统

**方式一：一键启动（推荐）**
```bash
python start_system.py
```

**方式二：分别启动**
```bash
# 终端1：启动后端
cd backend
python -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload

# 终端2：启动前端
cd frontend
npm install
npm start
```

### 4. 使用系统

1. 打开浏览器访问 `http://localhost:3000`
2. 填写应用名称和任务描述
3. 选择检测模式（视觉/XML）
4. 点击"Initialize Explorer"初始化
5. 点击"Start"开始自动探索
6. 观察实时截图和操作日志
7. 探索完成后查看UX评估报告

## 📱 使用界面

### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│                    AppAgent Real-time Explorer              │
├─────────────────┬───────────────────────────────────────────┤
│   控制面板       │              实时截图显示区域              │
│                 │                                           │
│ • 系统状态      │         [设备截图实时更新]                │
│ • 配置设置      │                                           │
│ • 操作控制      │                                           │
│                 ├───────────────────────────────────────────┤
│                 │              操作日志区域                  │
│                 │                                           │
│                 │ • AI决策过程                              │
│                 │ • 操作执行状态                            │
│                 │ • 系统消息                                │
├─────────────────┴───────────────────────────────────────────┤
│                    UX评估报告可视化                          │
│                                                             │
│ • 评分概览  • 关键问题  • 改进建议  • 可视化图表            │
└─────────────────────────────────────────────────────────────┘
```

### 功能说明

**控制面板**:
- 系统状态监控（连接状态、运行状态、当前轮次）
- 配置设置（应用名称、任务描述、检测模式）
- 操作控制（初始化、开始、停止、重置）

**实时截图**:
- 保持原始设备比例
- 显示AI标注的UI元素
- 支持before/after对比

**操作日志**:
- 实时显示AI思考过程
- 记录所有操作执行结果
- 彩色分类（信息/成功/警告/错误）

**UX评估报告**:
- 可用性评分
- 可访问性评分
- 用户体验评分
- 关键问题列表
- 改进建议
- 可视化图表

## 🔧 技术细节

### 后端技术栈
- **FastAPI**: 现代Python Web框架
- **WebSocket**: 实时双向通信
- **AsyncIO**: 异步处理
- **Pydantic**: 数据验证
- **OpenCV**: 图像处理

### 前端技术栈
- **React 18**: 现代前端框架
- **Ant Design**: UI组件库
- **WebSocket**: 实时通信
- **Recharts**: 数据可视化
- **Axios**: HTTP客户端

### 核心模块复用
- `self_explore.py`: 核心探索逻辑
- `and_controller.py`: Android设备控制
- `model.py`: AI模型交互
- `ux_evaluator.py`: UX评估引擎
- `ux_report_generator.py`: 报告生成器

## 📊 API接口

### REST API
- `GET /`: 系统状态
- `GET /devices`: 获取设备列表
- `POST /initialize`: 初始化探索器
- `POST /start`: 开始探索
- `POST /stop`: 停止探索

### WebSocket消息
- `screenshot_update`: 截图更新
- `ai_decision`: AI决策
- `action_executed`: 操作执行
- `report_generated`: 报告生成
- `error`: 错误消息

## 🔍 故障排除

### 常见问题

**1. 设备连接问题**
```bash
# 检查ADB连接
adb devices
# 重启ADB服务
adb kill-server && adb start-server
```

**2. YOLO服务未启动**
- 系统会自动回退到XML模式
- 或手动启动YOLO服务在端口7861

**3. WebSocket连接失败**
- 检查后端服务是否正常运行
- 确认端口8000未被占用

**4. 前端依赖安装失败**
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### 日志调试
- 后端日志：终端输出
- 前端日志：浏览器开发者工具
- WebSocket消息：网络面板

## 🎯 使用场景

1. **移动应用UX评估**: 自动化用户体验评估和报告生成
2. **应用功能测试**: AI驱动的功能探索和测试
3. **界面可用性分析**: 自动识别可用性问题
4. **用户行为模拟**: 模拟真实用户操作流程
5. **应用质量保证**: 持续的质量监控和评估

## 📈 扩展功能

系统支持以下扩展：
- 多设备并行探索
- 自定义评估指标
- 报告模板定制
- 操作录制回放
- 性能监控集成

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进系统功能。

## 📄 许可证

本项目基于原有AppAgent项目的许可证。
