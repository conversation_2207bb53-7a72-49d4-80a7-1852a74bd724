"""
UX数据可视化模块
生成UX评估数据的图表和可视化展示
"""

import os
import json
from typing import Dict, List, Tuple, Any
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
import numpy as np

from ux_evaluator import UXEvaluationResult


class UXVisualizer:
    """UX数据可视化器"""
    
    def __init__(self, output_dir: str = "ux_visualizations"):
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def create_ux_score_radar_chart(self, evaluation: UXEvaluationResult, 
                                   app_name: str = "Unknown App") -> str:
        """创建UX评分雷达图"""
        
        # 准备数据
        categories = ['可用性', '可访问性', '一致性', '用户体验', '任务完成']
        scores = [
            evaluation.usability_score / 10.0,
            evaluation.accessibility_score / 10.0,
            evaluation.consistency_score / 10.0,
            evaluation.user_experience_score / 10.0,
            evaluation.metrics.task_completion_rate
        ]
        
        # 创建雷达图
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        # 计算角度
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        scores += scores[:1]  # 闭合图形
        angles += angles[:1]
        
        # 绘制雷达图
        ax.plot(angles, scores, 'o-', linewidth=2, label=app_name, color='#2E86AB')
        ax.fill(angles, scores, alpha=0.25, color='#2E86AB')
        
        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 1)
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
        ax.grid(True)
        
        # 添加标题和图例
        plt.title(f'{app_name} - UX评分雷达图', size=16, fontweight='bold', pad=20)
        plt.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
        
        # 保存图表
        filename = f"ux_radar_chart_{app_name.replace(' ', '_')}.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def create_metrics_bar_chart(self, evaluation: UXEvaluationResult,
                                app_name: str = "Unknown App") -> str:
        """创建UX指标柱状图"""
        
        # 准备数据
        metrics = {
            '任务完成率': evaluation.metrics.task_completion_rate,
            '导航效率': evaluation.metrics.navigation_efficiency,
            '交互流畅度': evaluation.metrics.interaction_smoothness,
            '目标达成度': evaluation.metrics.goal_achievement_score,
            '错误率': 1 - evaluation.metrics.error_rate,  # 转换为正向指标
            '回退率': 1 - evaluation.metrics.back_rate,   # 转换为正向指标
        }
        
        # 创建柱状图
        fig, ax = plt.subplots(figsize=(12, 8))
        
        bars = ax.bar(metrics.keys(), metrics.values(), 
                     color=['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#4CAF50', '#FF9800'])
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.2f}', ha='center', va='bottom', fontweight='bold')
        
        # 设置图表样式
        ax.set_ylim(0, 1.1)
        ax.set_ylabel('评分 (0-1)', fontsize=12, fontweight='bold')
        ax.set_title(f'{app_name} - UX关键指标', fontsize=16, fontweight='bold', pad=20)
        ax.grid(axis='y', alpha=0.3)
        
        # 旋转x轴标签
        plt.xticks(rotation=45, ha='right')
        
        # 保存图表
        filename = f"ux_metrics_bar_{app_name.replace(' ', '_')}.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def create_action_distribution_pie(self, evaluation: UXEvaluationResult,
                                     app_name: str = "Unknown App") -> str:
        """创建操作类型分布饼图"""
        
        if not evaluation.metrics.action_distribution:
            return ""
        
        # 准备数据
        labels = list(evaluation.metrics.action_distribution.keys())
        sizes = list(evaluation.metrics.action_distribution.values())
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
        
        # 创建饼图
        fig, ax = plt.subplots(figsize=(10, 8))
        
        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors[:len(labels)],
                                         autopct='%1.1f%%', startangle=90)
        
        # 设置样式
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        ax.set_title(f'{app_name} - 操作类型分布', fontsize=16, fontweight='bold', pad=20)
        
        # 保存图表
        filename = f"ux_action_pie_{app_name.replace(' ', '_')}.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def create_decision_flow_chart(self, evaluation: UXEvaluationResult,
                                 app_name: str = "Unknown App") -> str:
        """创建决策流程图"""
        
        if not evaluation.metrics.decision_distribution:
            return ""
        
        # 准备数据
        decisions = evaluation.metrics.decision_distribution
        total_decisions = sum(decisions.values())
        
        # 创建流程图
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 定义决策类型的颜色和位置
        decision_colors = {
            'SUCCESS': '#4CAF50',
            'CONTINUE': '#2196F3', 
            'BACK': '#FF9800',
            'INEFFECTIVE': '#F44336',
            'ERROR': '#9C27B0'
        }
        
        y_positions = np.arange(len(decisions))
        bars = ax.barh(y_positions, list(decisions.values()), 
                      color=[decision_colors.get(d, '#666666') for d in decisions.keys()])
        
        # 添加百分比标签
        for i, (decision, count) in enumerate(decisions.items()):
            percentage = (count / total_decisions) * 100
            ax.text(count + 0.1, i, f'{count} ({percentage:.1f}%)', 
                   va='center', fontweight='bold')
        
        # 设置图表样式
        ax.set_yticks(y_positions)
        ax.set_yticklabels(decisions.keys())
        ax.set_xlabel('操作次数', fontsize=12, fontweight='bold')
        ax.set_title(f'{app_name} - 决策类型分布', fontsize=16, fontweight='bold', pad=20)
        ax.grid(axis='x', alpha=0.3)
        
        # 保存图表
        filename = f"ux_decision_flow_{app_name.replace(' ', '_')}.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def create_interaction_heatmap(self, screenshot_path: str, interaction_points: List[Tuple[int, int]],
                                 app_name: str = "Unknown App") -> str:
        """创建交互热力图"""
        
        if not os.path.exists(screenshot_path) or not interaction_points:
            return ""
        
        try:
            # 加载截图
            screenshot = Image.open(screenshot_path)
            width, height = screenshot.size
            
            # 创建热力图数据
            heatmap_data = np.zeros((height, width))
            
            # 为每个交互点添加热力
            for x, y in interaction_points:
                if 0 <= x < width and 0 <= y < height:
                    # 创建高斯分布的热力点
                    radius = 50
                    for dy in range(-radius, radius + 1):
                        for dx in range(-radius, radius + 1):
                            nx, ny = x + dx, y + dy
                            if 0 <= nx < width and 0 <= ny < height:
                                distance = np.sqrt(dx*dx + dy*dy)
                                if distance <= radius:
                                    intensity = np.exp(-(distance**2) / (2 * (radius/3)**2))
                                    heatmap_data[ny, nx] += intensity
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            # 显示原始截图
            ax1.imshow(screenshot)
            ax1.set_title('原始界面', fontsize=14, fontweight='bold')
            ax1.axis('off')
            
            # 显示热力图叠加
            ax2.imshow(screenshot)
            if heatmap_data.max() > 0:
                heatmap_normalized = heatmap_data / heatmap_data.max()
                ax2.imshow(heatmap_normalized, alpha=0.6, cmap='hot')
            
            # 标记交互点
            for x, y in interaction_points:
                ax2.plot(x, y, 'wo', markersize=8, markeredgecolor='black', markeredgewidth=2)
            
            ax2.set_title('交互热力图', fontsize=14, fontweight='bold')
            ax2.axis('off')
            
            plt.suptitle(f'{app_name} - 用户交互热力分析', fontsize=16, fontweight='bold')
            
            # 保存图表
            filename = f"ux_interaction_heatmap_{app_name.replace(' ', '_')}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            return filepath
            
        except Exception as e:
            print(f"Failed to create interaction heatmap: {e}")
            return ""
    
    def create_comprehensive_dashboard(self, evaluation: UXEvaluationResult,
                                     app_name: str = "Unknown App") -> str:
        """创建综合UX仪表板"""
        
        # 创建大型综合图表
        fig = plt.figure(figsize=(20, 12))
        
        # 1. UX评分雷达图 (左上)
        ax1 = plt.subplot(2, 3, 1, projection='polar')
        categories = ['可用性', '可访问性', '一致性', '用户体验', '任务完成']
        scores = [
            evaluation.usability_score / 10.0,
            evaluation.accessibility_score / 10.0,
            evaluation.consistency_score / 10.0,
            evaluation.user_experience_score / 10.0,
            evaluation.metrics.task_completion_rate
        ]
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        scores += scores[:1]
        angles += angles[:1]
        ax1.plot(angles, scores, 'o-', linewidth=2, color='#2E86AB')
        ax1.fill(angles, scores, alpha=0.25, color='#2E86AB')
        ax1.set_xticks(angles[:-1])
        ax1.set_xticklabels(categories)
        ax1.set_ylim(0, 1)
        ax1.set_title('UX综合评分', fontweight='bold', pad=20)
        
        # 2. 关键指标柱状图 (右上)
        ax2 = plt.subplot(2, 3, 2)
        metrics = {
            '完成率': evaluation.metrics.task_completion_rate,
            '效率': evaluation.metrics.navigation_efficiency,
            '流畅度': evaluation.metrics.interaction_smoothness,
        }
        bars = ax2.bar(metrics.keys(), metrics.values(), color=['#4CAF50', '#2196F3', '#FF9800'])
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.2f}', ha='center', va='bottom', fontweight='bold')
        ax2.set_ylim(0, 1.1)
        ax2.set_title('关键指标', fontweight='bold')
        ax2.grid(axis='y', alpha=0.3)
        
        # 3. 操作分布饼图 (左下)
        if evaluation.metrics.action_distribution:
            ax3 = plt.subplot(2, 3, 4)
            labels = list(evaluation.metrics.action_distribution.keys())
            sizes = list(evaluation.metrics.action_distribution.values())
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
            ax3.pie(sizes, labels=labels, colors=colors[:len(labels)], autopct='%1.1f%%')
            ax3.set_title('操作类型分布', fontweight='bold')
        
        # 4. 决策分布 (中下)
        if evaluation.metrics.decision_distribution:
            ax4 = plt.subplot(2, 3, 5)
            decisions = evaluation.metrics.decision_distribution
            decision_colors = {
                'SUCCESS': '#4CAF50', 'CONTINUE': '#2196F3', 
                'BACK': '#FF9800', 'INEFFECTIVE': '#F44336', 'ERROR': '#9C27B0'
            }
            bars = ax4.bar(decisions.keys(), decisions.values(),
                          color=[decision_colors.get(d, '#666666') for d in decisions.keys()])
            ax4.set_title('决策类型分布', fontweight='bold')
            plt.setp(ax4.get_xticklabels(), rotation=45, ha='right')
        
        # 5. 综合评分显示 (右下)
        ax5 = plt.subplot(2, 3, 6)
        ax5.text(0.5, 0.7, f'{evaluation.overall_ux_score:.2f}', 
                ha='center', va='center', fontsize=48, fontweight='bold', color='#2E86AB')
        ax5.text(0.5, 0.3, '综合UX评分', ha='center', va='center', fontsize=16, fontweight='bold')
        ax5.set_xlim(0, 1)
        ax5.set_ylim(0, 1)
        ax5.axis('off')
        
        # 添加总标题
        plt.suptitle(f'{app_name} - UX评估仪表板', fontsize=20, fontweight='bold', y=0.98)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        filename = f"ux_dashboard_{app_name.replace(' ', '_')}.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def generate_all_visualizations(self, evaluation: UXEvaluationResult,
                                  app_name: str = "Unknown App",
                                  screenshot_path: str = "",
                                  interaction_points: List[Tuple[int, int]] = None) -> Dict[str, str]:
        """生成所有可视化图表"""
        
        visualizations = {}
        
        try:
            # 生成各种图表
            visualizations['radar_chart'] = self.create_ux_score_radar_chart(evaluation, app_name)
            visualizations['metrics_bar'] = self.create_metrics_bar_chart(evaluation, app_name)
            visualizations['action_pie'] = self.create_action_distribution_pie(evaluation, app_name)
            visualizations['decision_flow'] = self.create_decision_flow_chart(evaluation, app_name)
            visualizations['dashboard'] = self.create_comprehensive_dashboard(evaluation, app_name)
            
            # 如果有截图和交互点，生成热力图
            if screenshot_path and interaction_points:
                visualizations['heatmap'] = self.create_interaction_heatmap(
                    screenshot_path, interaction_points, app_name)
            
            # 过滤掉空的文件路径
            visualizations = {k: v for k, v in visualizations.items() if v}
            
        except Exception as e:
            print(f"Error generating visualizations: {e}")
        
        return visualizations
