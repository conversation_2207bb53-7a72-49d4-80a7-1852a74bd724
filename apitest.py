from gradio_client import Client, handle_file
from icon_to_android_element import convert_to_android_elements

client = Client("http://127.0.0.1:7861/")
result = client.predict(
    image_input=handle_file("apps/Settings/demos/self_explore_2025-07-22_17-02-47/1_after.png"),
    box_threshold=0.05,
    iou_threshold=0.1,
    use_paddleocr=True,
    imgsz=640,
    api_name="/process"
)
res = result[1]
print(res)
ele = convert_to_android_elements(res)
print(ele)