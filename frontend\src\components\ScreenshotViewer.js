import React from 'react';
import { Card, Empty, Typography, Tag, Space } from 'antd';
import { MobileOutlined, CameraOutlined } from '@ant-design/icons';

const { Text } = Typography;

const ScreenshotViewer = ({ screenshot }) => {
  if (!screenshot) {
    return (
      <Card 
        title={
          <Space>
            <CameraOutlined />
            <span>Real-time Screenshot</span>
          </Space>
        }
        className="screenshot-card"
      >
        <Empty 
          image={<MobileOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          description="No screenshot available"
        />
      </Card>
    );
  }

  return (
    <Card 
      title={
        <Space>
          <CameraOutlined />
          <span>Real-time Screenshot</span>
          <Tag color="blue">Round {screenshot.round}</Tag>
          <Tag color={screenshot.stage === 'before' ? 'orange' : 'green'}>
            {screenshot.stage}
          </Tag>
        </Space>
      }
      className="screenshot-card"
    >
      <div className="screenshot-container">
        <img 
          src={screenshot.image} 
          alt={`Screenshot Round ${screenshot.round} - ${screenshot.stage}`}
          className="screenshot-image"
          style={{
            maxWidth: '100%',
            maxHeight: '70vh',
            objectFit: 'contain',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}
        />
      </div>
      <div style={{ marginTop: 12, textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          Path: {screenshot.path}
        </Text>
      </div>
    </Card>
  );
};

export default ScreenshotViewer;
