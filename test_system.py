#!/usr/bin/env python3
"""
AppAgent实时探索系统测试脚本
测试系统各个组件的功能和性能
"""

import asyncio
import json
import time
import requests
import websockets
from pathlib import Path
import subprocess
import sys

def print_colored(text, color):
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, '')}{text}{colors['end']}")

class SystemTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.ws_url = "ws://localhost:8000/ws"
        self.frontend_url = "http://localhost:3000"
        
    def test_backend_health(self):
        """测试后端健康状态"""
        print_colored("🔍 Testing backend health...", "blue")
        try:
            response = requests.get(f"{self.backend_url}/", timeout=5)
            if response.status_code == 200:
                print_colored("✅ Backend is healthy", "green")
                return True
            else:
                print_colored(f"❌ Backend returned status {response.status_code}", "red")
                return False
        except requests.exceptions.RequestException as e:
            print_colored(f"❌ Backend connection failed: {e}", "red")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        print_colored("🔍 Testing API endpoints...", "blue")
        
        endpoints = [
            ("/", "GET"),
            ("/devices", "GET"),
            ("/docs", "GET"),
        ]
        
        all_passed = True
        for endpoint, method in endpoints:
            try:
                if method == "GET":
                    response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                else:
                    response = requests.post(f"{self.backend_url}{endpoint}", timeout=5)
                
                if response.status_code in [200, 422]:  # 422 for validation errors is OK
                    print_colored(f"✅ {method} {endpoint}: {response.status_code}", "green")
                else:
                    print_colored(f"❌ {method} {endpoint}: {response.status_code}", "red")
                    all_passed = False
            except Exception as e:
                print_colored(f"❌ {method} {endpoint}: {e}", "red")
                all_passed = False
        
        return all_passed
    
    async def test_websocket_connection(self):
        """测试WebSocket连接"""
        print_colored("🔍 Testing WebSocket connection...", "blue")
        try:
            async with websockets.connect(self.ws_url) as websocket:
                print_colored("✅ WebSocket connection established", "green")
                
                # 发送测试消息
                test_message = {"type": "ping", "data": "test"}
                await websocket.send(json.dumps(test_message))
                print_colored("✅ Test message sent", "green")
                
                # 等待响应（超时5秒）
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print_colored("✅ WebSocket response received", "green")
                    return True
                except asyncio.TimeoutError:
                    print_colored("⚠️  WebSocket response timeout (this is normal)", "yellow")
                    return True
                    
        except Exception as e:
            print_colored(f"❌ WebSocket connection failed: {e}", "red")
            return False
    
    def test_frontend_availability(self):
        """测试前端可用性"""
        print_colored("🔍 Testing frontend availability...", "blue")
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                print_colored("✅ Frontend is available", "green")
                return True
            else:
                print_colored(f"❌ Frontend returned status {response.status_code}", "red")
                return False
        except requests.exceptions.RequestException as e:
            print_colored(f"❌ Frontend connection failed: {e}", "red")
            print_colored("💡 Make sure to run 'npm start' in the frontend directory", "yellow")
            return False
    
    def test_adb_connection(self):
        """测试ADB连接"""
        print_colored("🔍 Testing ADB connection...", "blue")
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            if result.returncode == 0:
                devices = [line for line in result.stdout.split('\n') if '\tdevice' in line]
                if devices:
                    print_colored(f"✅ Found {len(devices)} connected device(s)", "green")
                    for device in devices:
                        print_colored(f"   📱 {device.split()[0]}", "white")
                    return True
                else:
                    print_colored("⚠️  No devices connected", "yellow")
                    print_colored("💡 Connect an Android device via USB and enable USB debugging", "yellow")
                    return False
            else:
                print_colored("❌ ADB command failed", "red")
                return False
        except FileNotFoundError:
            print_colored("❌ ADB not found in PATH", "red")
            print_colored("💡 Install Android SDK Platform Tools", "yellow")
            return False
    
    def test_dependencies(self):
        """测试依赖项"""
        print_colored("🔍 Testing Python dependencies...", "blue")
        
        required_packages = [
            'fastapi',
            'uvicorn',
            'websockets',
            'pydantic',
            'requests',
            'opencv-python',
            'pillow',
            'pyyaml'
        ]
        
        all_installed = True
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print_colored(f"✅ {package}", "green")
            except ImportError:
                print_colored(f"❌ {package} not installed", "red")
                all_installed = False
        
        return all_installed
    
    def test_config_files(self):
        """测试配置文件"""
        print_colored("🔍 Testing configuration files...", "blue")
        
        config_files = [
            'config.yaml',
            'demo_config.yaml',
            'backend/app.py',
            'frontend/package.json',
            'requirements.txt'
        ]
        
        all_exist = True
        for config_file in config_files:
            if Path(config_file).exists():
                print_colored(f"✅ {config_file}", "green")
            else:
                print_colored(f"❌ {config_file} not found", "red")
                all_exist = False
        
        return all_exist
    
    async def run_all_tests(self):
        """运行所有测试"""
        print_colored("🧪 AppAgent Real-time System Tests", "purple")
        print_colored("="*50, "cyan")
        
        tests = [
            ("Configuration Files", self.test_config_files),
            ("Python Dependencies", self.test_dependencies),
            ("ADB Connection", self.test_adb_connection),
            ("Backend Health", self.test_backend_health),
            ("API Endpoints", self.test_api_endpoints),
            ("WebSocket Connection", self.test_websocket_connection),
            ("Frontend Availability", self.test_frontend_availability),
        ]
        
        results = {}
        for test_name, test_func in tests:
            print_colored(f"\n📋 {test_name}", "cyan")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                results[test_name] = result
            except Exception as e:
                print_colored(f"❌ Test failed with exception: {e}", "red")
                results[test_name] = False
        
        # 总结
        print_colored("\n" + "="*50, "cyan")
        print_colored("📊 Test Results Summary", "purple")
        print_colored("="*50, "cyan")
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print_colored(f"{status} {test_name}", "green" if result else "red")
        
        print_colored(f"\n🎯 Overall: {passed}/{total} tests passed", "green" if passed == total else "yellow")
        
        if passed == total:
            print_colored("🎉 All tests passed! System is ready to use.", "green")
        else:
            print_colored("⚠️  Some tests failed. Please check the issues above.", "yellow")
        
        return passed == total

def main():
    """主函数"""
    tester = SystemTester()
    
    try:
        result = asyncio.run(tester.run_all_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print_colored("\n🛑 Tests interrupted by user", "yellow")
        sys.exit(1)
    except Exception as e:
        print_colored(f"\n❌ Unexpected error: {e}", "red")
        sys.exit(1)

if __name__ == "__main__":
    main()
