<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="0" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/sd" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/pu0" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/pu3" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/h1u" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/we0" class="X.0qgK" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="true" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/kxr" class="android.widget.TabHost" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="6" hint=""><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/sg_" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="3" hint="" /></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/kx4" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/sg_" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/gb3" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/nug" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/g7i" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/we0" class="X.0qgK" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/fyt" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/r8h" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="2" hint="" /><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/ect" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="7" hint="" /><node index="2" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="8" hint="" /><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/r8d" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="4" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/p1p" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/kr7" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/fqr" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="1" hint="" /><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/edu" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="6" hint="" /><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/we3" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/we0" class="androidx.viewpager.widget.ViewPager" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="2" hint=""><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/view_rootview" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="2" hint=""><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/long_press_layout" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="Video" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="3" hint="" /><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/ljg" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="12" hint="" /><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/cga" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="20" hint="" /><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/n72" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="24" hint="" /><node index="5" text="" resource-id="com.ss.android.ugc.trill:id/y5" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="28" hint="" /><node index="6" text="" resource-id="com.ss.android.ugc.trill:id/w1f" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="53" hint="" /><node index="7" text="" resource-id="com.ss.android.ugc.trill:id/w1e" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="54" hint="" /><node index="8" text="" resource-id="com.ss.android.ugc.trill:id/w47" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="14" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/w5s" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/iju" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/pnt" class="android.widget.RelativeLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="1" hint="" /></node></node></node><node index="9" text="" resource-id="com.ss.android.ugc.trill:id/xu" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="33" hint="" /><node index="10" text="" resource-id="com.ss.android.ugc.trill:id/vzo" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="8" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/w5r" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/vyb" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="4" hint="" /><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/player_view" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/r2t" class="android.view.TextureView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="1" hint="" /></node><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/l51" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="3" hint="" /><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/nbj" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="6" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/nav" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1728]" drawing-order="1" hint="" /></node></node></node><node index="11" text="" resource-id="com.ss.android.ugc.trill:id/wn2" class="X.0tO9" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1852]" drawing-order="23" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/bpe" class="X.0tO9" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1635]" drawing-order="9" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ry6" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[39,963][891,1635]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/the" class="X.0tO9" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1635]" drawing-order="3" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ban" class="X.0tO9" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1156]" drawing-order="3" hint="" /><node NAF="true" index="1" text="" resource-id="com.ss.android.ugc.trill:id/jjm" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[520,0][560,78]" drawing-order="7" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/r_p" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[520,0][560,78]" drawing-order="1" hint="" /></node><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/ijt" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,613][1080,1635]" drawing-order="9" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[921,613][1080,828]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[921,613][1080,828]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/avn" class="android.widget.RelativeLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[921,634][1080,788]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/oas" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[921,634][1080,713]" drawing-order="5" hint="" /><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/g_p" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[929,639][1072,782]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/vow" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="Coasters+ profile" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[942,652][1059,769]" drawing-order="2" hint="" /></node><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/vpb" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[937,648][1063,774]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/ggo" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="Follow Coasters+" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,739][1079,828]" drawing-order="3" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ggy" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[964,739][1037,802]" drawing-order="2" hint="" /></node></node></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,828][1080,983]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/edw" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="Like video. 20.7K likes" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,828][1080,983]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/edr" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,828][1080,946]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/edp" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="Like" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[942,828][1060,946]" drawing-order="1" hint="" /></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[957,946][1044,983]" drawing-order="2" hint=""><node index="0" text="20.7K" resource-id="com.ss.android.ugc.trill:id/eds" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[957,946][1044,983]" drawing-order="1" hint="" /></node></node></node><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[922,983][1080,1154]" drawing-order="3" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/d_f" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="Read or add comments. 245 comments" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[922,983][1080,1154]" drawing-order="1" hint=""><node NAF="true" index="0" text="" resource-id="com.ss.android.ugc.trill:id/d69" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[922,983][1080,1106]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/d7c" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[942,988][1060,1106]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/d76" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[959,1005][1043,1089]" drawing-order="1" hint="" /></node></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[970,1106][1032,1154]" drawing-order="2" hint=""><node index="0" text="245" resource-id="com.ss.android.ugc.trill:id/d6c" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[970,1106][1032,1154]" drawing-order="1" hint="" /></node></node></node><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,1154][1080,1320]" drawing-order="4" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/fv2" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="Add or remove this video from Favorites." checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,1154][1080,1320]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/fuf" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,1154][1080,1272]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/fu_" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[942,1154][1060,1272]" drawing-order="1" hint="" /></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[961,1272][1040,1320]" drawing-order="2" hint=""><node index="0" text="2993" resource-id="com.ss.android.ugc.trill:id/fuh" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[961,1272][1040,1320]" drawing-order="1" hint="" /></node></node></node><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1349][1080,1598]" drawing-order="7" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/lo9" class="android.widget.RelativeLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1349][1080,1598]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/m4w" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[776,1349][1025,1598]" drawing-order="2" hint="" /><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/lo0" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="Sound: shine on by choppy.wav" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[915,1469][1080,1598]" drawing-order="3" hint=""><node NAF="true" index="0" text="" resource-id="com.ss.android.ugc.trill:id/lmi" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[915,1469][1080,1598]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/lmg" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[952,1493][1059,1598]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/lmj" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[952,1493][1059,1598]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/lmb" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[953,1493][1058,1598]" drawing-order="1" hint="" /></node></node></node></node></node></node><node index="5" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,1320][1080,1480]" drawing-order="5" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/edw" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="Share video. 2151 shares" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,1320][1080,1480]" drawing-order="1" hint=""><node NAF="true" index="0" text="" resource-id="com.ss.android.ugc.trill:id/qr2" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,1320][1080,1443]" drawing-order="1" hint=""><node NAF="true" index="0" text="" resource-id="com.ss.android.ugc.trill:id/qrf" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[959,1325][1043,1409]" drawing-order="1" hint="" /></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,1443][1080,1480]" drawing-order="2" hint=""><node index="0" text="2151" resource-id="com.ss.android.ugc.trill:id/qr5" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[922,1443][1080,1469]" drawing-order="1" hint="" /></node></node></node></node><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/az_" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1156][1080,1635]" drawing-order="4" hint=""><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/az9" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1156][817,1635]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ijq" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1156][817,1635]" drawing-order="1" hint=""><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1156][817,1259]" drawing-order="5" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/feed_multi_tag_layout" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1156][721,1259]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1156][721,1259]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/aio" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1156][721,1259]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ngm" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[43,1167][96,1220]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/dly" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[112,1167][700,1248]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/nly" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[112,1167][700,1212]" drawing-order="1" hint=""><node index="0" text="DollyWood Theme Park" resource-id="com.ss.android.ugc.trill:id/nm2" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[112,1167][498,1212]" drawing-order="1" hint="" /><node index="1" text="·" resource-id="com.ss.android.ugc.trill:id/nlz" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[509,1168][520,1210]" drawing-order="2" hint="" /><node index="2" text="Sevierville" resource-id="com.ss.android.ugc.trill:id/nm0" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[531,1167][700,1212]" drawing-order="3" hint="" /></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/nlq" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[112,1211][622,1248]" drawing-order="2" hint=""><node index="0" text="8907 people posted about this place" resource-id="com.ss.android.ugc.trill:id/nlp" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[112,1211][622,1248]" drawing-order="1" hint="" /></node></node></node></node></node></node><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1280][817,1338]" drawing-order="6" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/fzc" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1280][295,1338]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/pon" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1280][295,1338]" drawing-order="1" hint=""><node index="0" text="@Coasters+" resource-id="com.ss.android.ugc.trill:id/title" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1280][295,1338]" drawing-order="2" hint="" /></node></node></node><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/ded" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1359][817,1461]" drawing-order="7" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/dei" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1359][817,1461]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/e9g" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1359][817,1461]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/eaj" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1359][817,1461]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/e_k" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1359][817,1461]" drawing-order="1" hint=""><node index="0" text="#RiverRampage is a great way to beat the #Dollywood summer heat." resource-id="com.ss.android.ugc.trill:id/desc" class="com.bytedance.tux.input.TuxTextLayoutView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1359][817,1461]" drawing-order="2" hint="" /></node></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/cce" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1359][817,1361]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1359][817,1361]" drawing-order="2" hint="" /></node></node></node></node><node index="7" text="" resource-id="com.ss.android.ugc.trill:id/dee" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1503][817,1635]" drawing-order="14" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ddl" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1503][817,1598]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/k_e" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1503][817,1597]" drawing-order="1" hint=""><node index="0" text="Get notified of next LIVE" resource-id="com.ss.android.ugc.trill:id/k6g" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1503][817,1597]" drawing-order="1" hint="" /></node></node></node></node></node></node><node NAF="true" index="5" text="" resource-id="com.ss.android.ugc.trill:id/w5v" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[831,1386][1080,1635]" drawing-order="1" hint="" /></node><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/iji" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1635][1080,1729]" drawing-order="4" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/def" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1635][1080,1729]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ddk" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1635][1080,1729]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/fx4" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1635][1080,1729]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/q6j" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1635][997,1729]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/q6i" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1635][997,1729]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/dde" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="Search" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[32,1661][74,1703]" drawing-order="1" hint="" /><node index="1" text="Search · Dollywood Park" resource-id="com.ss.android.ugc.trill:id/q6h" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[85,1661][976,1703]" drawing-order="2" hint="" /></node></node><node index="1" text="" resource-id="" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[996,1635][1007,1646]" drawing-order="4" hint="" /><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/q0i" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1007,1661][1049,1703]" drawing-order="5" hint="" /></node></node></node></node><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/ijp" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1726][1080,1729]" drawing-order="5" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1726][1080,1729]" drawing-order="1" hint="" /></node></node><node index="12" text="" resource-id="com.ss.android.ugc.trill:id/gradual_top" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,357]" drawing-order="9" hint="" /><node index="13" text="" resource-id="com.ss.android.ugc.trill:id/fu_" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,136]" drawing-order="35" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/fua" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[944,0][1080,136]" drawing-order="1" hint="" /></node><node index="15" text="" resource-id="com.ss.android.ugc.trill:id/gradual_bottom" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1143][1080,1857]" drawing-order="22" hint="" /></node></node></node><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/u0d" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,294][1080,1556]" drawing-order="7" hint="" /></node></node></node><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/r8c" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][189,189]" drawing-order="1" hint="" /></node></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/t5v" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1857]" drawing-order="5" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/ro9" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,63]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,63][1080,215]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/pmj" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,63][1080,215]" drawing-order="1" hint=""><node NAF="true" index="0" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,65][147,212]" drawing-order="2" hint="" /><node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[147,63][933,215]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.HorizontalScrollView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[261,63][819,215]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[261,63][819,215]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="X.0qPf" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[261,63][392,215]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.ss.android.ugc.trill" content-desc="Shop" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[261,63][392,215]" drawing-order="1" hint=""><node index="0" text="Shop" resource-id="android:id/text1" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[261,63][392,215]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/ie0" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[329,97][392,181]" drawing-order="4" hint="" /></node></node><node index="1" text="" resource-id="" class="X.0qPf" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[392,63][637,215]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="Following" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[392,63][637,215]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[392,63][637,215]" drawing-order="1" hint=""><node index="0" text="Following" resource-id="android:id/text1" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[392,63][637,215]" drawing-order="1" hint="" /></node></node></node><node index="2" text="" resource-id="" class="X.0qPf" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[637,63][819,215]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="For You" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[637,63][819,215]" drawing-order="1" hint=""><node index="0" text="For You" resource-id="android:id/text1" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[637,63][819,215]" drawing-order="1" hint="" /></node></node></node></node></node><node NAF="true" index="2" text="" resource-id="com.ss.android.ugc.trill:id/hkf" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[933,65][1080,212]" drawing-order="3" hint="" /></node></node></node><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/title_shadow" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,63]" drawing-order="3" hint="" /></node></node></node><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/w3r" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1689][1080,1763]" drawing-order="8" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1689][1080,1763]" drawing-order="1" hint="" /></node><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/bon" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1727][1080,1728]" drawing-order="2" hint="" /><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/kx0" class="android.widget.LinearLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1728][1080,1857]" drawing-order="4" hint=""><node index="0" text="" resource-id="com.ss.android.ugc.trill:id/kws" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="Home" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[0,1728][216,1857]" drawing-order="1" hint=""><node index="0" text="Home" resource-id="" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[0,1808][216,1842]" drawing-order="2" hint="" /><node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[66,1733][150,1817]" drawing-order="1" hint="" /></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/kwr" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="Friends" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[216,1728][432,1857]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[355,1739][376,1760]" drawing-order="3" hint="" /><node index="1" text="Friends" resource-id="" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[216,1808][432,1842]" drawing-order="2" hint="" /><node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[282,1733][366,1817]" drawing-order="1" hint="" /></node><node index="2" text="" resource-id="com.ss.android.ugc.trill:id/kwp" class="android.widget.Button" package="com.ss.android.ugc.trill" content-desc="Create" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[432,1728][648,1857]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[441,1728][638,1857]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[441,1728][638,1857]" drawing-order="4" hint="" /><node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[540,1793][540,1793]" drawing-order="3" hint="" /></node><node index="3" text="" resource-id="com.ss.android.ugc.trill:id/kwt" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="Inbox" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[648,1728][864,1857]" drawing-order="4" hint=""><node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[787,1739][808,1760]" drawing-order="4" hint="" /><node index="1" text="Inbox" resource-id="" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[648,1808][864,1842]" drawing-order="2" hint="" /><node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[714,1733][798,1817]" drawing-order="1" hint="" /></node><node index="4" text="" resource-id="com.ss.android.ugc.trill:id/kwu" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="Profile" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[864,1728][1080,1857]" drawing-order="5" hint=""><node index="0" text="Profile" resource-id="" class="android.widget.TextView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[864,1808][1080,1842]" drawing-order="2" hint="" /><node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[930,1733][1014,1817]" drawing-order="1" hint="" /></node></node></node></node></node><node index="1" text="" resource-id="com.ss.android.ugc.trill:id/ojj" class="android.widget.FrameLayout" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][194,367]" drawing-order="3" hint="" /></node></node></node></node></node></node><node index="1" text="" resource-id="android:id/navigationBarBackground" class="android.view.View" package="com.ss.android.ugc.trill" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1857][1080,1920]" drawing-order="2" hint="" /></node></hierarchy>