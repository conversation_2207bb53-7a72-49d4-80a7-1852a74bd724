"""
实时自动化探索系统后端API
基于 self_explore.py 逻辑构建的实时前后端系统
"""

import asyncio
import json
import os
import sys
import time
import datetime
import base64
from typing import Dict, List, Optional
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn

# 添加scripts目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scripts'))

from config import load_config
from and_controller import AndroidController, list_all_devices
from model import OpenAIModel, QwenModel, parse_explore_rsp, parse_reflect_rsp
from utils import print_with_color, draw_bbox_multi
from ux_evaluator import UXEvaluator
from ux_report_generator import UXReportGenerator
from gradio_client import Client, handle_file
from icon_to_android_element import convert_to_android_elements
import prompts

app = FastAPI(title="AppAgent Real-time Explorer API")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局状态管理
class ExplorerState:
    def __init__(self):
        self.is_running = False
        self.current_round = 0
        self.task_description = ""
        self.controller: Optional[AndroidController] = None
        self.mllm = None
        self.ux_evaluator: Optional[UXEvaluator] = None
        self.ux_report_generator: Optional[UXReportGenerator] = None
        self.client = None
        self.configs = {}
        self.task_dir = ""
        self.last_act = "None"
        self.useless_list = set()
        self.task_complete = False
        self.connected_clients: List[WebSocket] = []
        
    def add_client(self, websocket: WebSocket):
        self.connected_clients.append(websocket)
        
    def remove_client(self, websocket: WebSocket):
        if websocket in self.connected_clients:
            self.connected_clients.remove(websocket)
            
    async def broadcast_message(self, message: dict):
        """向所有连接的客户端广播消息"""
        if self.connected_clients:
            disconnected = []
            for client in self.connected_clients:
                try:
                    await client.send_text(json.dumps(message))
                except:
                    disconnected.append(client)
            
            # 移除断开连接的客户端
            for client in disconnected:
                self.remove_client(client)

explorer_state = ExplorerState()

# 数据模型
class TaskRequest(BaseModel):
    task_description: str
    app_name: str
    detection_mode: str = "visual"  # "visual" or "xml"

class ActionResponse(BaseModel):
    success: bool
    message: str
    round_count: int
    screenshot_path: Optional[str] = None
    action_taken: Optional[str] = None

# API路由
@app.get("/")
async def root():
    return {"message": "AppAgent Real-time Explorer API"}

@app.get("/status")
async def get_status():
    """获取当前探索状态"""
    return {
        "is_running": explorer_state.is_running,
        "current_round": explorer_state.current_round,
        "task_description": explorer_state.task_description,
        "task_complete": explorer_state.task_complete,
        "has_controller": explorer_state.controller is not None,
        "connected_clients": len(explorer_state.connected_clients)
    }

@app.get("/devices")
async def get_devices():
    """获取可用的Android设备列表"""
    try:
        devices = list_all_devices()
        return {"devices": devices}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/initialize")
async def initialize_explorer(request: TaskRequest):
    """初始化探索器"""
    try:
        if explorer_state.is_running:
            raise HTTPException(status_code=400, detail="Explorer is already running")
        
        # 加载配置
        explorer_state.configs = load_config()
        
        # 初始化设备控制器
        devices = list_all_devices()
        if not devices:
            raise HTTPException(status_code=400, detail="No Android devices found")
        
        device = devices[0]  # 使用第一个设备
        explorer_state.controller = AndroidController(device)
        width, height = explorer_state.controller.get_device_size()
        
        # 初始化AI模型
        if explorer_state.configs["MODEL"] == "OpenAI":
            explorer_state.mllm = OpenAIModel(
                base_url=explorer_state.configs["OPENAI_API_BASE"],
                api_key=explorer_state.configs["OPENAI_API_KEY"],
                model=explorer_state.configs["OPENAI_API_MODEL"],
                temperature=explorer_state.configs["TEMPERATURE"],
                max_tokens=explorer_state.configs["MAX_TOKENS"]
            )
        elif explorer_state.configs["MODEL"] == "Qwen":
            explorer_state.mllm = QwenModel(
                api_key=explorer_state.configs["DASHSCOPE_API_KEY"],
                model=explorer_state.configs["QWEN_MODEL"]
            )
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported model type: {explorer_state.configs['MODEL']}")
        
        # 初始化YOLO客户端（如果使用视觉模式）
        if request.detection_mode == "visual":
            try:
                explorer_state.client = Client("http://127.0.0.1:7861/")
            except Exception as e:
                print_with_color(f"Warning: Could not connect to YOLO service: {e}", "yellow")
                request.detection_mode = "xml"  # 回退到XML模式
        
        # 创建工作目录
        root_dir = os.path.dirname(os.path.dirname(__file__))
        work_dir = os.path.join(root_dir, "apps", request.app_name)
        os.makedirs(work_dir, exist_ok=True)
        
        demo_dir = os.path.join(work_dir, "demos")
        os.makedirs(demo_dir, exist_ok=True)
        
        demo_timestamp = int(time.time())
        task_name = datetime.datetime.fromtimestamp(demo_timestamp).strftime("self_explore_%Y-%m-%d_%H-%M-%S")
        explorer_state.task_dir = os.path.join(demo_dir, task_name)
        os.makedirs(explorer_state.task_dir, exist_ok=True)
        
        # 初始化UX评估器
        ux_reports_dir = os.path.join(work_dir, "ux_reports")
        os.makedirs(ux_reports_dir, exist_ok=True)
        
        explorer_state.ux_evaluator = UXEvaluator(explorer_state.mllm, width, height)
        explorer_state.ux_report_generator = UXReportGenerator(ux_reports_dir)
        
        # 设置任务描述
        explorer_state.task_description = request.task_description
        
        return {
            "success": True,
            "message": "Explorer initialized successfully",
            "device": device,
            "screen_size": f"{width}x{height}",
            "detection_mode": request.detection_mode,
            "task_dir": explorer_state.task_dir
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/start")
async def start_exploration():
    """开始自动探索"""
    try:
        if explorer_state.is_running:
            raise HTTPException(status_code=400, detail="Explorer is already running")
        
        if not explorer_state.controller or not explorer_state.mllm:
            raise HTTPException(status_code=400, detail="Explorer not initialized")
        
        explorer_state.is_running = True
        explorer_state.current_round = 0
        explorer_state.last_act = "None"
        explorer_state.useless_list = set()
        explorer_state.task_complete = False
        
        # 在后台启动探索任务
        asyncio.create_task(run_exploration_loop())
        
        return {"success": True, "message": "Exploration started"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/stop")
async def stop_exploration():
    """停止自动探索"""
    explorer_state.is_running = False
    
    # 广播停止消息
    await explorer_state.broadcast_message({
        "type": "exploration_stopped",
        "message": "Exploration stopped by user"
    })
    
    return {"success": True, "message": "Exploration stopped"}

async def perform_exploration_step():
    """执行单步探索"""
    explorer_state.current_round += 1
    round_count = explorer_state.current_round

    # 广播步骤开始
    await explorer_state.broadcast_message({
        "type": "step_start",
        "data": {
            "round": round_count,
            "message": f"Starting round {round_count}"
        }
    })

    # 获取截图
    screenshot_before = explorer_state.controller.get_screenshot(f"{round_count}_before", explorer_state.task_dir)
    if screenshot_before == "ERROR":
        await explorer_state.broadcast_message({
            "type": "error",
            "message": "Failed to capture screenshot"
        })
        return

    # 检测UI元素
    elem_list = await detect_ui_elements(screenshot_before, round_count)
    if not elem_list:
        await explorer_state.broadcast_message({
            "type": "error",
            "message": "No UI elements detected"
        })
        return

    # 生成带标签的截图
    labeled_screenshot = os.path.join(explorer_state.task_dir, f"{round_count}_before_labeled.png")
    draw_bbox_multi(screenshot_before, labeled_screenshot, elem_list, dark_mode=explorer_state.configs["DARK_MODE"])

    # 发送截图到前端
    await send_screenshot_to_frontend(labeled_screenshot, round_count, "before")

    # AI决策
    action_result = await make_ai_decision(labeled_screenshot, elem_list, round_count)
    if not action_result:
        return

    # 执行动作
    await execute_action(action_result, elem_list, round_count)

async def detect_ui_elements(screenshot_path: str, round_count: int):
    """检测UI元素"""
    try:
        if explorer_state.client:  # 视觉模式
            elem_temp = explorer_state.client.predict(
                image_input=handle_file(screenshot_path),
                box_threshold=0.05,
                iou_threshold=0.1,
                use_paddleocr=True,
                imgsz=640,
                api_name="/process"
            )
            elem_yolo = elem_temp[1]
            width, height = explorer_state.controller.get_device_size()
            elem_list = convert_to_android_elements(elem_yolo, screen_width=width, screen_height=height)

            await explorer_state.broadcast_message({
                "type": "detection_result",
                "data": {
                    "mode": "visual",
                    "elements_found": len(elem_list),
                    "round": round_count
                }
            })
        else:  # XML模式
            # 这里可以添加XML模式的元素检测逻辑
            elem_list = []

        return elem_list

    except Exception as e:
        print_with_color(f"Element detection error: {e}", "red")
        await explorer_state.broadcast_message({
            "type": "error",
            "message": f"Element detection failed: {str(e)}"
        })
        return []

async def send_screenshot_to_frontend(screenshot_path: str, round_count: int, stage: str):
    """发送截图到前端"""
    try:
        # 读取截图并转换为base64
        with open(screenshot_path, "rb") as img_file:
            img_data = base64.b64encode(img_file.read()).decode()

        await explorer_state.broadcast_message({
            "type": "screenshot_update",
            "data": {
                "image": f"data:image/png;base64,{img_data}",
                "round": round_count,
                "stage": stage,
                "path": screenshot_path
            }
        })

    except Exception as e:
        print_with_color(f"Screenshot send error: {e}", "red")

async def make_ai_decision(labeled_screenshot: str, elem_list: list, round_count: int):
    """AI决策制定"""
    try:
        # 构建提示
        prompt = prompts.self_explore_task_template.replace("<task_description>", explorer_state.task_description)
        prompt = prompt.replace("<last_act>", explorer_state.last_act)

        await explorer_state.broadcast_message({
            "type": "ai_thinking",
            "data": {
                "round": round_count,
                "message": "AI is analyzing the screen..."
            }
        })

        # 获取AI响应
        status, response = explorer_state.mllm.get_model_response(prompt, [labeled_screenshot])

        if status:
            # 解析响应
            parsed_result = parse_explore_rsp(response)

            await explorer_state.broadcast_message({
                "type": "ai_decision",
                "data": {
                    "round": round_count,
                    "action": parsed_result[0] if parsed_result else "UNKNOWN",
                    "response": response,
                    "elements_available": len(elem_list)
                }
            })

            return parsed_result
        else:
            await explorer_state.broadcast_message({
                "type": "error",
                "message": f"AI decision failed: {response}"
            })
            return None

    except Exception as e:
        print_with_color(f"AI decision error: {e}", "red")
        await explorer_state.broadcast_message({
            "type": "error",
            "message": f"AI decision error: {str(e)}"
        })
        return None

async def run_exploration_loop():
    """运行探索循环的异步任务"""
    try:
        while explorer_state.is_running and explorer_state.current_round < explorer_state.configs["MAX_ROUNDS"]:
            await perform_exploration_step()

            if explorer_state.task_complete:
                break

            # 等待间隔
            await asyncio.sleep(explorer_state.configs["REQUEST_INTERVAL"])

        # 探索完成，生成最终报告
        await generate_final_report()

    except Exception as e:
        print_with_color(f"Exploration loop error: {e}", "red")
        await explorer_state.broadcast_message({
            "type": "error",
            "message": f"Exploration error: {str(e)}"
        })
    finally:
        explorer_state.is_running = False

async def execute_action(action_result: list, elem_list: list, round_count: int):
    """执行AI决定的动作"""
    try:
        if not action_result:
            return

        act_name = action_result[0]
        explorer_state.last_act = action_result[-1] if len(action_result) > 1 else act_name

        await explorer_state.broadcast_message({
            "type": "action_start",
            "data": {
                "round": round_count,
                "action": act_name,
                "description": explorer_state.last_act
            }
        })

        if act_name == "FINISH":
            explorer_state.task_complete = True
            await explorer_state.broadcast_message({
                "type": "task_complete",
                "data": {
                    "round": round_count,
                    "message": "Task completed successfully"
                }
            })
            return

        # 执行具体动作
        if act_name == "tap" and len(action_result) > 1:
            area = action_result[1]
            if 1 <= area <= len(elem_list):
                elem = elem_list[area - 1]
                center_x = (elem.bbox[0][0] + elem.bbox[1][0]) // 2
                center_y = (elem.bbox[0][1] + elem.bbox[1][1]) // 2
                result = explorer_state.controller.tap(center_x, center_y)

                await explorer_state.broadcast_message({
                    "type": "action_executed",
                    "data": {
                        "round": round_count,
                        "action": "tap",
                        "coordinates": [center_x, center_y],
                        "element": area,
                        "result": result
                    }
                })

        elif act_name == "text" and len(action_result) > 2:
            area = action_result[1]
            text_input = action_result[2]
            if 1 <= area <= len(elem_list):
                elem = elem_list[area - 1]
                center_x = (elem.bbox[0][0] + elem.bbox[1][0]) // 2
                center_y = (elem.bbox[0][1] + elem.bbox[1][1]) // 2

                # 先点击输入框
                explorer_state.controller.tap(center_x, center_y)
                await asyncio.sleep(1)
                # 输入文本
                result = explorer_state.controller.text(text_input)

                await explorer_state.broadcast_message({
                    "type": "action_executed",
                    "data": {
                        "round": round_count,
                        "action": "text",
                        "coordinates": [center_x, center_y],
                        "element": area,
                        "text": text_input,
                        "result": result
                    }
                })

        elif act_name == "long_press" and len(action_result) > 1:
            area = action_result[1]
            if 1 <= area <= len(elem_list):
                elem = elem_list[area - 1]
                center_x = (elem.bbox[0][0] + elem.bbox[1][0]) // 2
                center_y = (elem.bbox[0][1] + elem.bbox[1][1]) // 2
                result = explorer_state.controller.long_press(center_x, center_y)

                await explorer_state.broadcast_message({
                    "type": "action_executed",
                    "data": {
                        "round": round_count,
                        "action": "long_press",
                        "coordinates": [center_x, center_y],
                        "element": area,
                        "result": result
                    }
                })

        elif act_name == "swipe" and len(action_result) > 2:
            area = action_result[1]
            direction = action_result[2]
            if 1 <= area <= len(elem_list):
                elem = elem_list[area - 1]
                center_x = (elem.bbox[0][0] + elem.bbox[1][0]) // 2
                center_y = (elem.bbox[0][1] + elem.bbox[1][1]) // 2
                result = explorer_state.controller.swipe(center_x, center_y, direction)

                await explorer_state.broadcast_message({
                    "type": "action_executed",
                    "data": {
                        "round": round_count,
                        "action": "swipe",
                        "coordinates": [center_x, center_y],
                        "element": area,
                        "direction": direction,
                        "result": result
                    }
                })

        # 等待动作完成后获取新截图
        await asyncio.sleep(2)
        screenshot_after = explorer_state.controller.get_screenshot(f"{round_count}_after", explorer_state.task_dir)
        if screenshot_after != "ERROR":
            await send_screenshot_to_frontend(screenshot_after, round_count, "after")

            # 记录UX交互数据
            if explorer_state.ux_evaluator and act_name != "FINISH":
                try:
                    explorer_state.ux_evaluator.add_interaction_data(
                        step=round_count,
                        action_type=act_name,
                        target_element=elem_list[action_result[1] - 1].uid if len(action_result) > 1 and action_result[1] <= len(elem_list) else None,
                        decision="CONTINUE",
                        screenshot_before=os.path.join(explorer_state.task_dir, f"{round_count}_before_labeled.png"),
                        screenshot_after=screenshot_after,
                        response_time=explorer_state.configs["REQUEST_INTERVAL"]
                    )
                except Exception as e:
                    print_with_color(f"Failed to record UX data: {e}", "red")

    except Exception as e:
        print_with_color(f"Action execution error: {e}", "red")
        await explorer_state.broadcast_message({
            "type": "error",
            "message": f"Action execution failed: {str(e)}"
        })

async def generate_final_report():
    """生成最终的UX评估报告"""
    try:
        if not explorer_state.ux_evaluator or not explorer_state.ux_report_generator:
            return

        await explorer_state.broadcast_message({
            "type": "report_generation_start",
            "data": {
                "message": "Generating UX evaluation report..."
            }
        })

        # 生成UX评估
        evaluation = explorer_state.ux_evaluator.evaluate_ux(explorer_state.task_description)

        # 保存报告
        saved_files = explorer_state.ux_report_generator.save_report(
            evaluation,
            app_name="AppAgent_Exploration",
            format_type="both",
            include_visualizations=True
        )

        await explorer_state.broadcast_message({
            "type": "report_generated",
            "data": {
                "evaluation": {
                    "overall_score": evaluation.overall_ux_score,
                    "usability_score": evaluation.usability_score,
                    "accessibility_score": evaluation.accessibility_score,
                    "user_experience_score": evaluation.user_experience_score,
                    "critical_issues": evaluation.critical_issues[:5],  # 前5个关键问题
                    "recommendations": evaluation.recommendations[:5]  # 前5个建议
                },
                "files": saved_files,
                "message": "UX evaluation report generated successfully"
            }
        })

    except Exception as e:
        print_with_color(f"Report generation error: {e}", "red")
        await explorer_state.broadcast_message({
            "type": "error",
            "message": f"Report generation failed: {str(e)}"
        })

# WebSocket连接管理
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    explorer_state.add_client(websocket)
    
    try:
        # 发送初始状态
        await websocket.send_text(json.dumps({
            "type": "status_update",
            "data": {
                "is_running": explorer_state.is_running,
                "current_round": explorer_state.current_round,
                "task_description": explorer_state.task_description
            }
        }))
        
        # 保持连接
        while True:
            await websocket.receive_text()
            
    except WebSocketDisconnect:
        explorer_state.remove_client(websocket)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
