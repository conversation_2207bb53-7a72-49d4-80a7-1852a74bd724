"""
UX指标计算模块
用于计算和分析移动应用的用户体验量化指标
"""

import json
import time
import math
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class ActionType(Enum):
    TAP = "tap"
    TEXT = "text"
    LONG_PRESS = "long_press"
    SWIPE = "swipe"
    BACK = "back"
    FINISH = "finish"


class DecisionType(Enum):
    SUCCESS = "SUCCESS"
    CONTINUE = "CONTINUE"
    BACK = "BACK"
    INEFFECTIVE = "INEFFECTIVE"
    ERROR = "ERROR"


@dataclass
class ActionRecord:
    """单个操作记录"""
    step: int
    action_type: ActionType
    target_element: Optional[str]
    timestamp: float
    success: bool
    decision: DecisionType
    response_time: float = 0.0
    error_message: Optional[str] = None


@dataclass
class UXMetricsResult:
    """UX指标计算结果"""
    # 基础指标
    task_completion_rate: float
    total_steps: int
    successful_steps: int
    error_rate: float
    back_rate: float
    
    # 效率指标
    average_response_time: float
    task_efficiency_score: float
    navigation_efficiency: float
    
    # 用户体验指标
    user_frustration_index: float
    interaction_smoothness: float
    goal_achievement_score: float
    
    # 详细统计
    action_distribution: Dict[str, int]
    decision_distribution: Dict[str, int]
    error_patterns: List[str]
    
    # 综合评分
    overall_ux_score: float


class UXMetrics:
    """UX指标计算器"""
    
    def __init__(self):
        self.action_records: List[ActionRecord] = []
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.task_completed: bool = False
        
    def add_action_record(self, step: int, action_type: str, target_element: Optional[str],
                         decision: str, response_time: float = 0.0, 
                         error_message: Optional[str] = None):
        """添加操作记录"""
        try:
            # 特殊处理FINISH操作（可能是大写）
            if action_type.upper() == "FINISH":
                action_enum = ActionType.FINISH
            else:
                action_enum = ActionType(action_type.lower())
        except ValueError:
            action_enum = ActionType.TAP  # 默认值
            
        try:
            decision_enum = DecisionType(decision.upper())
        except ValueError:
            decision_enum = DecisionType.CONTINUE  # 默认值
            
        success = decision_enum in [DecisionType.SUCCESS, DecisionType.CONTINUE]
        
        record = ActionRecord(
            step=step,
            action_type=action_enum,
            target_element=target_element,
            timestamp=time.time(),
            success=success,
            decision=decision_enum,
            response_time=response_time,
            error_message=error_message
        )

        # 将记录添加到列表中
        self.action_records.append(record)

        # 检查是否为FINISH操作
        if action_enum == ActionType.FINISH:
            self.task_completed = True
            self.end_time = time.time()
            print(f"DEBUG: Task marked as completed due to FINISH action at step {step}")

        # 设置开始时间
        if self.start_time is None:
            self.start_time = record.timestamp
    
    def calculate_task_completion_rate(self) -> float:
        """计算任务完成率"""
        if not self.action_records:
            return 0.0
        return 1.0 if self.task_completed else 0.0
    
    def calculate_error_rate(self) -> float:
        """计算错误率"""
        if not self.action_records:
            return 0.0
        
        error_count = sum(1 for record in self.action_records 
                         if record.decision in [DecisionType.ERROR, DecisionType.INEFFECTIVE])
        return error_count / len(self.action_records)
    
    def calculate_back_rate(self) -> float:
        """计算回退率"""
        if not self.action_records:
            return 0.0
        
        back_count = sum(1 for record in self.action_records 
                        if record.decision == DecisionType.BACK)
        return back_count / len(self.action_records)
    
    def calculate_navigation_efficiency(self) -> float:
        """计算导航效率"""
        if not self.action_records:
            return 0.0
        
        successful_actions = sum(1 for record in self.action_records 
                               if record.decision == DecisionType.SUCCESS)
        total_actions = len(self.action_records)
        
        # 考虑回退和无效操作的惩罚
        penalty = self.calculate_back_rate() + self.calculate_error_rate()
        efficiency = (successful_actions / total_actions) * (1 - penalty)
        
        return max(0.0, min(1.0, efficiency))
    
    def calculate_user_frustration_index(self) -> float:
        """计算用户挫败感指数"""
        if not self.action_records:
            return 0.0
        
        frustration_factors = 0.0
        
        # 连续错误操作增加挫败感
        consecutive_errors = 0
        max_consecutive_errors = 0
        
        for record in self.action_records:
            if record.decision in [DecisionType.ERROR, DecisionType.INEFFECTIVE, DecisionType.BACK]:
                consecutive_errors += 1
                max_consecutive_errors = max(max_consecutive_errors, consecutive_errors)
            else:
                consecutive_errors = 0
        
        # 挫败感基于最大连续错误数和总错误率（降低权重，适应AI agent）
        frustration_factors += max_consecutive_errors * 0.2  # 降低连续错误的影响
        frustration_factors += self.calculate_error_rate() * 0.3  # 降低总错误率的影响
        frustration_factors += self.calculate_back_rate() * 0.15  # 降低回退率的影响

        # 响应时间影响（新增，但权重很低）
        response_times = [r.response_time for r in self.action_records if r.response_time > 0]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            # 只有当响应时间超过12秒时才增加挫折感
            if avg_response_time > 12.0:
                time_frustration = min(0.2, (avg_response_time - 12.0) / 40.0)  # 最多增加0.2
                frustration_factors += time_frustration

        return min(1.0, frustration_factors)
    
    def calculate_interaction_smoothness(self) -> float:
        """计算交互流畅度 - 针对AI agent优化，放宽时间阈值"""
        if len(self.action_records) < 2:
            return 0.9  # 提高单步操作的默认流畅度

        # 基于响应时间和操作连续性
        response_times = [record.response_time for record in self.action_records
                         if record.response_time > 0]

        if not response_times:
            return 0.8  # 默认值

        avg_response_time = sum(response_times) / len(response_times)

        # 针对AI agent调整时间阈值：
        # - 3秒以内：优秀 (0.9-1.0)
        # - 3-8秒：良好 (0.7-0.9)
        # - 8-15秒：一般 (0.5-0.7)
        # - 15秒以上：较差 (0.3-0.5)
        if avg_response_time <= 3.0:
            smoothness = 0.9 + (3.0 - avg_response_time) / 30.0  # 最高1.0
        elif avg_response_time <= 8.0:
            smoothness = 0.7 + (8.0 - avg_response_time) / 25.0  # 0.7-0.9
        elif avg_response_time <= 15.0:
            smoothness = 0.5 + (15.0 - avg_response_time) / 35.0  # 0.5-0.7
        else:
            smoothness = max(0.3, 0.5 - (avg_response_time - 15.0) / 60.0)  # 最低0.3

        # 考虑操作连续性（减少错误和回退的影响）
        continuity_penalty = (self.calculate_error_rate() + self.calculate_back_rate()) * 0.3  # 降低惩罚
        smoothness *= (1 - continuity_penalty)

        return max(0.3, min(1.0, smoothness))  # 确保最低0.3分
    
    def calculate_goal_achievement_score(self) -> float:
        """计算目标达成分数"""
        completion_score = self.calculate_task_completion_rate()
        efficiency_score = self.calculate_navigation_efficiency()
        
        # 综合完成度和效率
        return (completion_score * 0.7) + (efficiency_score * 0.3)
    
    def get_action_distribution(self) -> Dict[str, int]:
        """获取操作类型分布"""
        distribution = {}
        for record in self.action_records:
            action_name = record.action_type.value
            distribution[action_name] = distribution.get(action_name, 0) + 1
        return distribution
    
    def get_decision_distribution(self) -> Dict[str, int]:
        """获取决策类型分布"""
        distribution = {}
        for record in self.action_records:
            decision_name = record.decision.value
            distribution[decision_name] = distribution.get(decision_name, 0) + 1
        return distribution
    
    def identify_error_patterns(self) -> List[str]:
        """识别错误模式"""
        patterns = []
        
        # 检查连续相同错误
        consecutive_same_errors = {}
        current_error = None
        count = 0
        
        for record in self.action_records:
            if record.decision in [DecisionType.ERROR, DecisionType.INEFFECTIVE]:
                if record.action_type == current_error:
                    count += 1
                else:
                    if current_error and count >= 2:
                        patterns.append(f"连续{count}次{current_error.value}操作失败")
                    current_error = record.action_type
                    count = 1
            else:
                if current_error and count >= 2:
                    patterns.append(f"连续{count}次{current_error.value}操作失败")
                current_error = None
                count = 0
        
        # 检查高频回退
        if self.calculate_back_rate() > 0.3:
            patterns.append("频繁回退操作，可能存在导航问题")
        
        # 检查高错误率
        if self.calculate_error_rate() > 0.4:
            patterns.append("高错误率，界面可用性存在问题")
        
        return patterns
    
    def calculate_comprehensive_metrics(self) -> UXMetricsResult:
        """计算综合UX指标"""
        if not self.action_records:
            return UXMetricsResult(
                task_completion_rate=0.0,
                total_steps=0,
                successful_steps=0,
                error_rate=0.0,
                back_rate=0.0,
                average_response_time=0.0,
                task_efficiency_score=0.0,
                navigation_efficiency=0.0,
                user_frustration_index=0.0,
                interaction_smoothness=0.0,
                goal_achievement_score=0.0,
                action_distribution={},
                decision_distribution={},
                error_patterns=[],
                overall_ux_score=0.0
            )
        
        # 计算各项指标
        completion_rate = self.calculate_task_completion_rate()
        error_rate = self.calculate_error_rate()
        back_rate = self.calculate_back_rate()
        navigation_efficiency = self.calculate_navigation_efficiency()
        frustration_index = self.calculate_user_frustration_index()
        interaction_smoothness = self.calculate_interaction_smoothness()
        goal_achievement = self.calculate_goal_achievement_score()
        
        # 计算平均响应时间
        response_times = [r.response_time for r in self.action_records if r.response_time > 0]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0.0
        
        # 计算任务效率分数 - 针对AI agent调整时间基准
        total_time = (self.end_time - self.start_time) if self.end_time and self.start_time else 0.0
        # 将时间基准从5分钟调整为10分钟，更适合AI agent的响应特点
        time_penalty = min(total_time / 600.0, 0.8)  # 最多扣除80%分数
        task_efficiency = navigation_efficiency * (1 - time_penalty)
        
        # 计算综合UX分数
        overall_score = (
            completion_rate * 0.25 +
            navigation_efficiency * 0.20 +
            (1 - error_rate) * 0.15 +
            (1 - back_rate) * 0.10 +
            interaction_smoothness * 0.15 +
            (1 - frustration_index) * 0.15
        )
        
        return UXMetricsResult(
            task_completion_rate=completion_rate,
            total_steps=len(self.action_records),
            successful_steps=sum(1 for r in self.action_records if r.success),
            error_rate=error_rate,
            back_rate=back_rate,
            average_response_time=avg_response_time,
            task_efficiency_score=task_efficiency,
            navigation_efficiency=navigation_efficiency,
            user_frustration_index=frustration_index,
            interaction_smoothness=interaction_smoothness,
            goal_achievement_score=goal_achievement,
            action_distribution=self.get_action_distribution(),
            decision_distribution=self.get_decision_distribution(),
            error_patterns=self.identify_error_patterns(),
            overall_ux_score=overall_score
        )
