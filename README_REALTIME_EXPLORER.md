# AppAgent 实时探索系统

基于 `self_explorer.py` 重写的完整前后端实时探索系统，支持实时显示和双检测模式。

## 🚀 功能特性

### 核心功能
- **实时Web界面**: 现代化的Web界面，实时显示探索过程
- **双检测模式**: 支持XML模式和视觉模式（YOLO）
- **实时数据传输**: 使用WebSocket进行实时通信
- **会话管理**: 完整的探索会话管理系统
- **智能UX评估**: 多维度用户体验评估系统

### UX评估系统
- **界面可用性评估**: 分析界面布局、元素可见性、交互友好性
- **交互效率评估**: 评估操作步骤、响应时间、任务完成效率
- **可访问性评估**: 检查界面可访问性、元素标签、导航便利性
- **实时评分**: 动态显示各项UX指标评分
- **最终报告**: 任务完成后生成综合UX评估报告
- **历史记录**: 完整的评估历史追踪
- **可视化图表**: 多维度数据图表展示
- **报告导出**: 一键导出可视化评估报告

### 检测模式对比
| 模式 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **XML模式** | 快速精确、完整属性信息 | 可能遗漏视觉元素 | 标准Android应用 |
| **视觉模式** | 检测所有可见元素、支持OCR | 需要YOLO服务 | 复杂界面、游戏应用 |

## 📁 文件结构

```
AppAgent/
├── realtime_explorer_server.py      # Flask-SocketIO后端服务器
├── templates/
│   └── realtime_explorer.html       # 前端Web界面
├── scripts/
│   └── realtime_explorer.py         # 实时探索脚本
├── start_realtime_explorer.py       # 一键启动脚本
└── README_REALTIME_EXPLORER.md      # 本文档
```

## 🛠️ 安装依赖

```bash
pip install flask flask-socketio gradio-client psutil
```

## 🚀 快速开始

### 方法1: 一键启动（推荐）
```bash
python start_realtime_explorer.py
```

### 方法2: 手动启动
1. 启动后端服务器：
```bash
python realtime_explorer_server.py
```

2. 打开浏览器访问：http://localhost:5000

## 📱 使用步骤

### 1. 准备工作
- 确保Android设备通过ADB连接
- 如使用视觉模式，确保YOLO服务运行在 http://127.0.0.1:7861/

### 2. 配置探索任务
1. 在Web界面中填写：
   - **应用名称**: 目标应用名（如：clock, calculator）
   - **任务描述**: 详细的任务描述
   - **检测模式**: 选择XML模式或视觉模式

2. 点击"开始探索"按钮

### 3. 实时监控
- **实时截图**: 查看当前屏幕状态和元素标注
- **实时日志**: 监控探索过程和AI决策
- **统计信息**: 查看步骤数、元素数、操作数等
- **运行时间**: 实时显示探索持续时间
- **UX评估面板**: 实时查看用户体验评估结果

### 4. UX评估功能详解

#### 评估维度
1. **界面可用性** (每3步评估一次)
   - 界面布局合理性
   - 元素可见性和可访问性
   - 视觉层次和信息架构
   - 用户界面一致性

2. **交互效率** (每5步评估一次)
   - 操作步骤优化程度
   - 任务完成路径效率
   - 响应时间和性能
   - 错误率和重试次数

3. **可访问性** (每7步评估一次)
   - 元素标签完整性
   - 导航便利性
   - 视觉对比度
   - 触摸目标大小

#### 评分系统
- **9.0-10.0**: 优秀 (绿色)
- **7.0-8.9**: 良好 (蓝色)
- **5.0-6.9**: 一般 (黄色)
- **0.0-4.9**: 需改进 (红色)

#### 最终报告内容
- **执行摘要**: 整体UX表现概述
- **关键发现**: 主要UX问题和亮点
- **任务完成分析**: 完成状态、时间、效率
- **交互模式分析**: 操作类型、频率、导航模式
- **性能指标**: 响应时间、错误率、成功率
- **改进建议**: 具体的UX优化建议

### 5. 可视化图表系统

#### 图表类型
1. **评分趋势图** (折线图)
   - 显示各维度评分随时间的变化趋势
   - 支持总体评分、可用性、效率、可访问性四条曲线
   - 实时更新，直观展示UX改进过程

2. **评分雷达图** (雷达图)
   - 多维度评分的综合展示
   - 快速识别强项和弱项
   - 平衡性分析一目了然

3. **评估类型分布图** (环形图)
   - 显示各类型评估的执行次数
   - 了解评估覆盖度和重点
   - 彩色编码便于区分

4. **性能指标图** (柱状图)
   - 响应时间、错误率、成功率可视化
   - 性能趋势一目了然
   - 支持平均值计算

#### 图表特性
- **实时更新**: 评估数据产生时自动更新图表
- **交互式**: 支持悬停查看详细数值
- **响应式**: 适配不同屏幕尺寸
- **专业级**: 使用Chart.js提供高质量图表
- **颜色编码**: 统一的颜色主题，便于识别

#### 导出功能
- **一键导出**: 点击导出按钮生成完整报告
- **包含内容**:
  - 评估摘要统计
  - 所有可视化图表
  - 评分统计信息
  - 时间戳和应用信息
- **多种格式**: 支持打印和PDF保存
- **专业排版**: 适合展示和存档的格式

## 🔧 API接口

### REST API
- `POST /api/start_exploration` - 启动探索会话
- `POST /api/stop_exploration` - 停止探索会话
- `GET /api/session_status` - 获取会话状态

### WebSocket事件
- `connected` - 连接建立
- `exploration_started` - 探索开始
- `exploration_stopped` - 探索停止
- `screenshot_update` - 截图更新
- `action_update` - 操作更新
- `elements_update` - 元素数量更新
- `log_update` - 日志更新
- `ux_evaluation` - UX评估更新
- `task_complete` - 任务完成

## 🎯 命令行使用

也可以直接使用命令行运行探索脚本：

```bash
# 交互式模式选择
python scripts/realtime_explorer.py --app clock

# 指定检测模式
python scripts/realtime_explorer.py --app clock --detection_mode visual

# Socket模式（用于服务器集成）
python scripts/realtime_explorer.py --app clock --detection_mode visual --socket_mode true
```

## 📊 实时数据格式

### 截图数据
```json
{
  "type": "screenshot",
  "data": {
    "step_number": 1,
    "image_path": "1_before.png",
    "image_base64": "base64_encoded_image"
  }
}
```

### 操作数据
```json
{
  "type": "action",
  "data": {
    "step_number": 1,
    "action_type": "tap",
    "details": ["tap", 2],
    "available_elements": 15
  }
}
```

### 日志数据
```json
{
  "type": "log",
  "data": {
    "message": "AI is thinking...",
    "level": "info"
  }
}
```

### UX评估数据
```json
{
  "type": "ux_evaluation",
  "data": {
    "type": "interface_usability",
    "score": 8.5,
    "step_number": 3,
    "data": {
      "detailed_analysis": {
        "overall_score": 8.5,
        "summary": "界面整体可用性良好",
        "strengths": ["布局清晰", "元素可见性好"],
        "weaknesses": ["部分按钮过小"],
        "recommendations": ["增大触摸目标", "优化颜色对比"]
      }
    }
  }
}
```

### 最终UX报告数据
```json
{
  "type": "ux_evaluation",
  "data": {
    "type": "final_report",
    "score": 8.2,
    "step_number": 15,
    "data": {
      "overall_score": 8.2,
      "usability_score": 8.5,
      "efficiency_score": 7.8,
      "accessibility_score": 8.3,
      "summary": "应用整体用户体验良好",
      "key_findings": ["导航清晰", "响应及时", "可访问性佳"],
      "task_completion_analysis": {
        "completion_status": "成功",
        "completion_time": "2分30秒",
        "step_efficiency": "高效"
      },
      "recommendations": ["优化加载速度", "增强视觉反馈"]
    }
  }
}
```

## 🔍 故障排除

### 常见问题

1. **无法连接设备**
   - 检查ADB连接：`adb devices`
   - 确保USB调试已开启

2. **YOLO服务连接失败**
   - 确保YOLO服务运行在正确端口
   - 检查网络连接

3. **Web界面无法访问**
   - 检查端口5000是否被占用
   - 确认防火墙设置

4. **探索过程中断**
   - 查看控制台错误信息
   - 检查设备屏幕是否锁定

### 调试模式
启动时添加调试参数：
```bash
python realtime_explorer_server.py --debug
```

## 🎨 界面特性

- **现代化设计**: 渐变背景、毛玻璃效果
- **响应式布局**: 支持不同屏幕尺寸
- **实时状态指示**: 运行状态可视化
- **彩色日志**: 不同级别的日志颜色区分
- **统计面板**: 实时数据统计展示
- **可视化图表**: 专业级数据图表展示
- **交互式面板**: 可折叠的UX评估面板
- **一键导出**: 便捷的报告导出功能

## 🔄 与原版对比

| 特性 | 原版 self_explorer.py | 实时探索系统 |
|------|----------------------|-------------|
| 界面 | 命令行 | Web界面 |
| 实时性 | 无 | 实时显示 |
| 检测模式 | 单一 | 双模式选择 |
| 会话管理 | 无 | 完整管理 |
| 数据传输 | 本地文件 | WebSocket |
| 用户体验 | 基础 | 现代化 |
| UX评估 | 基础文本 | 多维度分析 |
| 数据可视化 | 无 | 专业图表 |
| 报告导出 | 无 | 一键导出 |
| 历史追踪 | 无 | 完整记录 |

## 📝 更新日志

### v1.1.0 (当前版本)
- **新增可视化功能**:
  - 评分趋势图 (折线图)
  - 评分雷达图 (多维度展示)
  - 评估类型分布图 (环形图)
  - 性能指标图 (柱状图)
- **新增导出功能**:
  - 一键导出可视化报告
  - 专业排版和打印支持
  - 包含统计摘要和图表
- **界面优化**:
  - 可折叠的UX评估面板
  - 实时图表更新
  - 响应式图表布局

### v1.0.0
- 完整的前后端系统
- 双检测模式支持
- 实时WebSocket通信
- 现代化Web界面
- 会话管理系统
- 多维度UX评估集成

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个系统！

## 📄 许可证

遵循原项目许可证
