import React, { useState } from 'react';
import { Card, Row, Col, Statistic, Progress, List, Tag, Collapse, Typography, Space, Button } from 'antd';
import { 
  TrophyOutlined, 
  EyeOutlined, 
  UserOutlined, 
  BugOutlined, 
  BulbOutlined,
  DownloadOutlined,
  Bar<PERSON>hartOutlined
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

const { Panel } = Collapse;
const { Title, Text, Paragraph } = Typography;

const UXReportViewer = ({ report }) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (!report || !report.evaluation) {
    return null;
  }

  const { evaluation, files } = report;

  // 准备雷达图数据
  const radarData = [
    {
      subject: 'Usability',
      score: evaluation.usability_score,
      fullMark: 10
    },
    {
      subject: 'Accessibility',
      score: evaluation.accessibility_score,
      fullMark: 10
    },
    {
      subject: 'User Experience',
      score: evaluation.user_experience_score,
      fullMark: 10
    },
    {
      subject: 'Overall',
      score: evaluation.overall_score,
      fullMark: 10
    }
  ];

  const getScoreColor = (score) => {
    if (score >= 8) return '#52c41a';
    if (score >= 6) return '#faad14';
    return '#ff4d4f';
  };

  const downloadReport = (fileType) => {
    if (files[fileType]) {
      // 这里可以实现文件下载逻辑
      console.log(`Downloading ${fileType} report:`, files[fileType]);
    }
  };

  return (
    <Card 
      title={
        <Space>
          <TrophyOutlined />
          <span>UX Evaluation Report</span>
          <Tag color="blue">Generated</Tag>
        </Space>
      }
      extra={
        <Space>
          <Button 
            icon={<DownloadOutlined />} 
            size="small"
            onClick={() => downloadReport('markdown')}
          >
            Markdown
          </Button>
          <Button 
            icon={<DownloadOutlined />} 
            size="small"
            onClick={() => downloadReport('json')}
          >
            JSON
          </Button>
        </Space>
      }
    >
      <Collapse defaultActiveKey={['overview']} ghost>
        <Panel 
          header={
            <Space>
              <BarChartOutlined />
              <span>Score Overview</span>
            </Space>
          } 
          key="overview"
        >
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="Overall Score"
                      value={evaluation.overall_score}
                      precision={1}
                      suffix="/ 10"
                      valueStyle={{ 
                        color: getScoreColor(evaluation.overall_score),
                        fontSize: '24px'
                      }}
                    />
                    <Progress 
                      percent={evaluation.overall_score * 10} 
                      strokeColor={getScoreColor(evaluation.overall_score)}
                      size="small"
                      showInfo={false}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="Usability"
                      value={evaluation.usability_score}
                      precision={1}
                      suffix="/ 10"
                      valueStyle={{ 
                        color: getScoreColor(evaluation.usability_score),
                        fontSize: '18px'
                      }}
                    />
                    <Progress 
                      percent={evaluation.usability_score * 10} 
                      strokeColor={getScoreColor(evaluation.usability_score)}
                      size="small"
                      showInfo={false}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="Accessibility"
                      value={evaluation.accessibility_score}
                      precision={1}
                      suffix="/ 10"
                      valueStyle={{ 
                        color: getScoreColor(evaluation.accessibility_score),
                        fontSize: '18px'
                      }}
                    />
                    <Progress 
                      percent={evaluation.accessibility_score * 10} 
                      strokeColor={getScoreColor(evaluation.accessibility_score)}
                      size="small"
                      showInfo={false}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="User Experience"
                      value={evaluation.user_experience_score}
                      precision={1}
                      suffix="/ 10"
                      valueStyle={{ 
                        color: getScoreColor(evaluation.user_experience_score),
                        fontSize: '18px'
                      }}
                    />
                    <Progress 
                      percent={evaluation.user_experience_score * 10} 
                      strokeColor={getScoreColor(evaluation.user_experience_score)}
                      size="small"
                      showInfo={false}
                    />
                  </Card>
                </Col>
              </Row>
            </Col>
            <Col span={12}>
              <Card size="small" title="Score Distribution">
                <ResponsiveContainer width="100%" height={200}>
                  <RadarChart data={radarData}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="subject" />
                    <PolarRadiusAxis 
                      angle={90} 
                      domain={[0, 10]} 
                      tick={false}
                    />
                    <Radar
                      name="Score"
                      dataKey="score"
                      stroke="#1890ff"
                      fill="#1890ff"
                      fillOpacity={0.3}
                    />
                  </RadarChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>
        </Panel>

        <Panel 
          header={
            <Space>
              <BugOutlined />
              <span>Critical Issues</span>
              <Tag color="red">{evaluation.critical_issues?.length || 0}</Tag>
            </Space>
          } 
          key="issues"
        >
          <List
            size="small"
            dataSource={evaluation.critical_issues || []}
            renderItem={(issue, index) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<BugOutlined style={{ color: '#ff4d4f' }} />}
                  title={`Issue ${index + 1}`}
                  description={issue}
                />
              </List.Item>
            )}
            locale={{ emptyText: 'No critical issues found' }}
          />
        </Panel>

        <Panel 
          header={
            <Space>
              <BulbOutlined />
              <span>Recommendations</span>
              <Tag color="green">{evaluation.recommendations?.length || 0}</Tag>
            </Space>
          } 
          key="recommendations"
        >
          <List
            size="small"
            dataSource={evaluation.recommendations || []}
            renderItem={(recommendation, index) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<BulbOutlined style={{ color: '#52c41a' }} />}
                  title={`Recommendation ${index + 1}`}
                  description={recommendation}
                />
              </List.Item>
            )}
            locale={{ emptyText: 'No recommendations available' }}
          />
        </Panel>
      </Collapse>
    </Card>
  );
};

export default UXReportViewer;
