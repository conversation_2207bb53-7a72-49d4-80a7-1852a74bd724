<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="26">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="joblib" />
            <item index="2" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="3" class="java.lang.String" itemvalue="matplotlib" />
            <item index="4" class="java.lang.String" itemvalue="opencv_contrib_python" />
            <item index="5" class="java.lang.String" itemvalue="torch" />
            <item index="6" class="java.lang.String" itemvalue="numpy" />
            <item index="7" class="java.lang.String" itemvalue="torchvision" />
            <item index="8" class="java.lang.String" itemvalue="opencv_python" />
            <item index="9" class="java.lang.String" itemvalue="Pillow" />
            <item index="10" class="java.lang.String" itemvalue="chromadb" />
            <item index="11" class="java.lang.String" itemvalue="langgraph" />
            <item index="12" class="java.lang.String" itemvalue="transformers" />
            <item index="13" class="java.lang.String" itemvalue="gradio" />
            <item index="14" class="java.lang.String" itemvalue="langchain-openai" />
            <item index="15" class="java.lang.String" itemvalue="opencv-python" />
            <item index="16" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="17" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="18" class="java.lang.String" itemvalue="pandas" />
            <item index="19" class="java.lang.String" itemvalue="openai" />
            <item index="20" class="java.lang.String" itemvalue="neo4j" />
            <item index="21" class="java.lang.String" itemvalue="asyncio" />
            <item index="22" class="java.lang.String" itemvalue="pure-python-adb" />
            <item index="23" class="java.lang.String" itemvalue="langchain" />
            <item index="24" class="java.lang.String" itemvalue="aiohttp" />
            <item index="25" class="java.lang.String" itemvalue="lxml" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>