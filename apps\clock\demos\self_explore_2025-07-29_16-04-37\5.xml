<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="0" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/action_bar_root" class="android.widget.LinearLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="1" hint=""><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/content" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/app_bar_layout" class="android.widget.LinearLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,231]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/toolbar" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,63][1080,231]" drawing-order="1" hint=""><node index="0" text="Alarm" resource-id="com.google.android.deskclock:id/action_bar_title" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[63,104][218,189]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.support.v7.widget.LinearLayoutCompat" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[975,73][1080,220]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/overflow_action_button" class="android.widget.ImageView" package="com.google.android.deskclock" content-desc="More options" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[975,83][1080,209]" drawing-order="1" hint="" /></node></node></node><node index="1" text="" resource-id="com.google.android.deskclock:id/desk_clock_pager_frame" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,231][1080,1647]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/desk_clock_pager" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,231][1080,1647]" drawing-order="6" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,231][1080,1647]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,231][1080,1647]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/main" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,231][1080,1647]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/alarm_recycler_view" class="android.support.v7.widget.RecyclerView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,231][1080,1647]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.google.android.deskclock" content-desc=" Alarm" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[42,252][1038,1338]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/alarm_card_layout" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[42,252][1038,1338]" drawing-order="1" hint=""><node index="0" text="Add label" resource-id="com.google.android.deskclock:id/edit_label" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="No label specified" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,252][891,378]" drawing-order="8" hint="Add label" /><node index="1" text="" resource-id="com.google.android.deskclock:id/arrow" class="android.widget.ImageButton" package="com.google.android.deskclock" content-desc="Collapse 7:00 AM alarm" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[891,252][996,378]" drawing-order="10" hint="" /><node index="2" text="7:00 AM" resource-id="com.google.android.deskclock:id/digital_clock" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="7:00 AM" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,378][415,561]" drawing-order="9" hint="" /><node index="3" text="" resource-id="com.google.android.deskclock:id/alarm_info" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,561][850,687]" drawing-order="12" hint=""><node index="0" text="Tomorrow" resource-id="com.google.android.deskclock:id/repeat_summary" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="Tomorrow" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,597][850,652]" drawing-order="1" hint="" /></node><node index="4" text="" resource-id="com.google.android.deskclock:id/onoff" class="android.widget.Switch" package="com.google.android.deskclock" content-desc="7:00 AM alarm" checkable="true" checked="true" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[850,561][996,687]" drawing-order="11" hint="" /><node index="5" text="" resource-id="com.google.android.deskclock:id/repeat_days" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[74,687][1006,813]" drawing-order="13" hint=""><node index="0" text="S" resource-id="com.google.android.deskclock:id/day_button_0" class="android.widget.CheckBox" package="com.google.android.deskclock" content-desc="Sunday" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[74,687][200,813]" drawing-order="1" hint="" /><node index="1" text="M" resource-id="com.google.android.deskclock:id/day_button_1" class="android.widget.CheckBox" package="com.google.android.deskclock" content-desc="Monday" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[208,687][334,813]" drawing-order="2" hint="" /><node index="2" text="T" resource-id="com.google.android.deskclock:id/day_button_2" class="android.widget.CheckBox" package="com.google.android.deskclock" content-desc="Tuesday" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[343,687][469,813]" drawing-order="3" hint="" /><node index="3" text="W" resource-id="com.google.android.deskclock:id/day_button_3" class="android.widget.CheckBox" package="com.google.android.deskclock" content-desc="Wednesday" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[477,687][603,813]" drawing-order="4" hint="" /><node index="4" text="T" resource-id="com.google.android.deskclock:id/day_button_4" class="android.widget.CheckBox" package="com.google.android.deskclock" content-desc="Thursday" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[611,687][737,813]" drawing-order="5" hint="" /><node index="5" text="F" resource-id="com.google.android.deskclock:id/day_button_5" class="android.widget.CheckBox" package="com.google.android.deskclock" content-desc="Friday" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[746,687][872,813]" drawing-order="6" hint="" /><node index="6" text="S" resource-id="com.google.android.deskclock:id/day_button_6" class="android.widget.CheckBox" package="com.google.android.deskclock" content-desc="Saturday" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[880,687][1006,813]" drawing-order="7" hint="" /></node><node index="6" text="" resource-id="com.google.android.deskclock:id/blackout_item" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,813][1017,939]" drawing-order="14" hint=""><node index="0" text="" resource-id="" class="android.view.View" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,813][891,939]" drawing-order="1" hint="" /><node index="1" text="Schedule alarm" resource-id="com.google.android.deskclock:id/blackout_title" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,813][469,939]" drawing-order="2" hint="" /><node index="2" text="" resource-id="com.google.android.deskclock:id/blackout_action" class="android.widget.ImageButton" package="com.google.android.deskclock" content-desc="remove scheduled date" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[891,813][1017,939]" drawing-order="4" hint="" /></node><node index="7" text="Default (Cesium)" resource-id="com.google.android.deskclock:id/choose_ringtone" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="Ringtone Default (Cesium)" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,939][996,1065]" drawing-order="15" hint="" /><node index="8" text="Vibrate" resource-id="com.google.android.deskclock:id/vibrate_onoff" class="android.widget.CheckBox" package="com.google.android.deskclock" content-desc="" checkable="true" checked="true" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1065][996,1191]" drawing-order="17" hint="" /><node index="9" text="Delete" resource-id="com.google.android.deskclock:id/delete" class="android.widget.Button" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1191][351,1317]" drawing-order="22" hint="" /></node></node><node index="1" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.google.android.deskclock" content-desc=" Alarm" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[42,1359][1038,1647]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/alarm_card_layout" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[42,1359][1038,1647]" drawing-order="1" hint=""><node index="0" text="9:00 AM" resource-id="com.google.android.deskclock:id/digital_clock" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="9:00 AM" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1401][414,1584]" drawing-order="9" hint="" /><node index="1" text="" resource-id="com.google.android.deskclock:id/arrow" class="android.widget.ImageButton" package="com.google.android.deskclock" content-desc="Expand 9:00 AM alarm" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[891,1359][996,1485]" drawing-order="10" hint="" /><node index="2" text="" resource-id="com.google.android.deskclock:id/alarm_info" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1584][850,1647]" drawing-order="12" hint=""><node index="0" text="" resource-id="" class="android.view.View" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1620][850,1647]" drawing-order="1" hint="" /><node index="1" text="Sun, Sat" resource-id="com.google.android.deskclock:id/days_of_week" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="Sunday, Saturday" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1620][242,1647]" drawing-order="4" hint="" /></node><node index="3" text="" resource-id="com.google.android.deskclock:id/onoff" class="android.widget.Switch" package="com.google.android.deskclock" content-desc="9:00 AM alarm" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[850,1584][996,1647]" drawing-order="11" hint="" /></node></node></node></node></node></node></node><node index="1" text="" resource-id="com.google.android.deskclock:id/fab_container" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1311][1080,1647]" drawing-order="7" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/fab" class="android.widget.Button" package="com.google.android.deskclock" content-desc="Add alarm" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[414,1353][666,1605]" drawing-order="1" hint="" /></node></node><node index="2" text="" resource-id="com.google.android.deskclock:id/navigation_bar" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1647][1080,1920]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1647][1080,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/tab_menu_alarm" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="Alarm" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[0,1647][216,1857]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[24,1679][192,1763]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_active_indicator_view" class="android.view.View" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[24,1679][192,1763]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[76,1689][139,1752]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[63,1779][153,1857]" drawing-order="2" hint=""><node index="0" text="Alarm" resource-id="com.google.android.deskclock:id/navigation_bar_item_large_label_view" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[63,1779][153,1829]" drawing-order="2" hint="" /></node></node><node index="1" text="" resource-id="com.google.android.deskclock:id/tab_menu_clock" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="Clock" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[216,1647][432,1857]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[240,1679][408,1763]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[292,1689][355,1752]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[281,1779][367,1857]" drawing-order="2" hint=""><node index="0" text="Clock" resource-id="com.google.android.deskclock:id/navigation_bar_item_small_label_view" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[281,1779][367,1829]" drawing-order="1" hint="" /></node></node><node index="2" text="" resource-id="com.google.android.deskclock:id/tab_menu_timer" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="Timer" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[432,1647][648,1857]" drawing-order="3" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[456,1679][624,1763]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[508,1689][571,1752]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[496,1779][583,1857]" drawing-order="2" hint=""><node index="0" text="Timer" resource-id="com.google.android.deskclock:id/navigation_bar_item_small_label_view" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[496,1779][583,1829]" drawing-order="1" hint="" /></node></node><node index="3" text="" resource-id="com.google.android.deskclock:id/tab_menu_stopwatch" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="Stopwatch" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[648,1647][864,1857]" drawing-order="4" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[672,1679][840,1763]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[724,1689][787,1752]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[675,1779][837,1857]" drawing-order="2" hint=""><node index="0" text="Stopwatch" resource-id="com.google.android.deskclock:id/navigation_bar_item_small_label_view" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[675,1779][837,1829]" drawing-order="1" hint="" /></node></node><node index="4" text="" resource-id="com.google.android.deskclock:id/tab_menu_bedtime" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="Bedtime" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[864,1647][1080,1857]" drawing-order="5" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[888,1679][1056,1763]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[940,1689][1003,1752]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.google.android.deskclock:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[908,1779][1035,1857]" drawing-order="2" hint=""><node index="0" text="Bedtime" resource-id="com.google.android.deskclock:id/navigation_bar_item_small_label_view" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[908,1779][1035,1829]" drawing-order="1" hint="" /></node></node></node></node></node></node></node></node></node></node></node></hierarchy>