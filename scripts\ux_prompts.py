"""
UX评估专用提示模板
用于分析移动应用的用户体验质量
"""

# 界面可用性评估模板
interface_usability_template = """您是一位UX专家，正在分析移动应用界面的可用性。
您将获得一个带有标记交互元素的移动应用截图。您的任务是基于以下标准评估界面可用性：

1. **元素可访问性**: 交互元素是否清晰可见且大小合适？
2. **布局组织**: 界面布局是否逻辑清晰且组织良好？
3. **视觉层次**: 是否有清晰的视觉层次来引导用户注意力？
4. **触摸目标大小**: 触摸目标是否足够大以便舒适交互？
5. **信息密度**: 信息密度是否合适，既不过于拥挤也不过于稀疏？

当前任务背景: <task_description>
屏幕分辨率: <screen_resolution>
交互元素数量: <element_count>

请分析截图并按以下格式提供评估：
可访问性评分: <1-10分及说明>
布局评分: <1-10分及说明>
视觉层次评分: <1-10分及说明>
触摸目标评分: <1-10分及说明>
信息密度评分: <1-10分及说明>
整体可用性评分: <1-10平均分>
主要问题: <主要可用性问题列表>
改进建议: <具体改进建议>
"""

# 操作流程效率评估模板
interaction_efficiency_template = """您是一位UX专家，正在分析移动应用中用户交互的效率。
您将获得用户操作前后的截图，以及正在执行任务的背景信息。

执行的操作: <action_type>
目标元素: <target_element>
任务背景: <task_description>
步骤编号: 第<step_number>步，预计共<total_steps>步
之前的操作: <previous_actions>

请基于以下标准评估交互效率：
1. **操作适当性**: 这是实现目标最有效的操作吗？
2. **导航效率**: 这个操作是否让用户更接近目标？
3. **认知负荷**: 理解这一步需要多少心理努力？
4. **错误预防**: 界面是否有助于防止用户错误？
5. **反馈质量**: 操作结果是否有清晰的反馈？

请按以下格式提供分析：
操作适当性: <1-10分及说明>
导航效率: <1-10分及说明>
认知负荷: <1-10分，越低越好，附说明>
错误预防: <1-10分及说明>
反馈质量: <1-10分及说明>
效率评分: <计算得出的总体评分>
替代操作: <如有更高效的替代方案请建议>
流程优化: <改进交互流程的建议>
"""

# 用户体验质量评估模板
user_experience_template = """您是一位UX专家，正在对移动应用的交互序列进行全面的用户体验评估。
您将分析显示用户在应用中操作过程的多个截图。

正在执行的任务: <task_description>
总步骤数: <total_steps>
成功完成: <completion_status>
时间背景: <time_taken>
错误发生次数: <error_count>

请基于以下标准评估整体用户体验：
1. **易学性**: 用户学习界面的难易程度？
2. **效率性**: 有经验的用户执行任务的速度？
3. **易记性**: 记住如何使用界面的难易程度？
4. **错误恢复**: 应用帮助用户从错误中恢复的能力？
5. **满意度**: 交互过程的愉悦度和满意度？

分析格式:
易学性评分: <1-10分及说明>
效率性评分: <1-10分及说明>
易记性评分: <1-10分及说明>
错误恢复评分: <1-10分及说明>
满意度评分: <1-10分及说明>
整体UX评分: <加权平均分>
优势: <积极的UX方面列表>
不足: <UX问题列表>
优先改进项: <最重要修复项的排序列表>
"""

# 可访问性评估模板
accessibility_evaluation_template = """您是一位可访问性专家，正在评估移动应用界面是否符合可访问性指南。
请分析提供的截图中的可访问性问题。

屏幕背景: <task_description>
元素数量: <element_count>
屏幕尺寸: <screen_resolution>

请基于以下标准评估可访问性：
1. **视觉对比度**: 文本和背景颜色的对比度是否足够？
2. **文本可读性**: 文本是否足够大且清晰易读？
3. **触摸目标大小**: 交互元素是否至少为44x44像素？
4. **元素标签**: 交互元素是否有适当的标签？
5. **导航支持**: 是否支持键盘/屏幕阅读器导航？

可访问性分析:
视觉对比度: <通过/失败及详细说明>
文本可读性: <通过/失败及详细说明>
触摸目标大小: <通过/失败及详细说明>
元素标签: <通过/失败及详细说明>
导航支持: <通过/失败及详细说明>
可访问性评分: <1-10总体评分>
关键问题: <可访问性障碍列表>
合规级别: <AA/AAA合规性评估>
修复步骤: <需要的具体修复措施>
"""

# 界面一致性评估模板
consistency_evaluation_template = """您是一位UX专家，正在评估移动应用不同屏幕间的界面一致性。
您将获得来自同一应用会话的多个截图。

应用背景: <app_name>
分析的屏幕数: <screen_count>
任务流程: <task_description>

请评估以下方面的一致性：
1. **视觉设计**: 颜色、字体、间距和视觉元素
2. **交互模式**: 按钮行为、导航方法
3. **信息架构**: 内容组织和标签
4. **反馈机制**: 应用对用户操作的响应方式

一致性分析:
视觉设计一致性: <1-10分及说明>
交互模式一致性: <1-10分及说明>
信息架构一致性: <1-10分及说明>
反馈一致性: <1-10分及说明>
整体一致性评分: <平均分>
发现的不一致性: <具体不一致性列表>
设计系统缺口: <设计系统需要改进的领域>
标准化建议: <更好一致性的建议>
"""

# 综合UX评估报告模板
comprehensive_ux_report_template = """您是一位资深UX顾问，正在为移动应用准备综合UX评估报告。
基于用户交互分析、界面截图和任务完成数据，请提供详细评估。

应用名称: <app_name>
评估周期: <evaluation_timeframe>
分析的任务: <task_list>
用户操作总数: <action_count>
成功率: <success_rate>
平均任务时间: <average_time>

## 执行摘要
<提供UX发现的高层次概述>

## 详细发现

### 可用性评估
<详细的可用性分析>

### 用户体验质量
<整体UX质量评估>

### 可访问性合规性
<可访问性评估结果>

### 性能效率
<任务完成和效率分析>

## 关键指标
- 整体UX评分: <1-10>
- 可用性评分: <1-10>
- 可访问性评分: <1-10>
- 效率评分: <1-10>
- 用户满意度: <1-10>

## 关键问题
<高优先级UX问题列表>

## 改进建议
<优先级排序的改进列表>

## 实施路线图
<UX改进的建议时间表>
"""
