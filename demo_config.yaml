# AppAgent实时探索系统演示配置
# 这是一个示例配置文件，展示如何配置系统

# AI模型配置
MODEL: "Qwen"  # 支持 "OpenAI" 或 "Qwen"

# OpenAI配置（如果使用OpenAI模型）
OPENAI_API_BASE: "https://api.openai.com/v1/chat/completions"
OPENAI_API_KEY: "sk-your-openai-api-key-here"  # 替换为你的OpenAI API密钥
OPENAI_API_MODEL: "gpt-4-vision-preview"
MAX_TOKENS: 300
TEMPERATURE: 0.0

# Qwen配置（如果使用Qwen模型）
DASHSCOPE_API_KEY: "sk-your-dashscope-api-key-here"  # 替换为你的DashScope API密钥
QWEN_MODEL: "qwen-vl-max"

# Android设备配置
ANDROID_SCREENSHOT_DIR: "/sdcard"  # Android设备上的截图存储目录
ANDROID_XML_DIR: "/sdcard"         # Android设备上的XML文件存储目录

# 探索参数
MAX_ROUNDS: 20          # 最大探索轮次
REQUEST_INTERVAL: 3     # 请求间隔（秒）
MIN_DIST: 30           # 元素间最小距离
DARK_MODE: false       # 是否为深色模式
DOC_REFINE: false      # 是否精炼文档

# 系统配置
DEBUG: true            # 调试模式
LOG_LEVEL: "INFO"      # 日志级别

# 演示任务示例
DEMO_TASKS:
  - name: "设置应用探索"
    description: "打开设置应用，浏览各个设置选项，了解应用的功能结构"
    app_name: "Settings"
    
  - name: "计算器功能测试"
    description: "使用计算器应用进行基本的数学运算，测试各个按钮功能"
    app_name: "Calculator"
    
  - name: "相机应用探索"
    description: "打开相机应用，探索拍照、录像等功能，测试不同模式"
    app_name: "Camera"
    
  - name: "浏览器使用测试"
    description: "打开浏览器，进行网页浏览，测试搜索、书签等功能"
    app_name: "Browser"

# UX评估配置
UX_EVALUATION:
  enabled: true                    # 是否启用UX评估
  generate_report: true           # 是否生成报告
  include_visualizations: true    # 是否包含可视化
  metrics:
    - usability                   # 可用性
    - accessibility              # 可访问性
    - efficiency                 # 效率
    - satisfaction              # 满意度
    - learnability              # 易学性

# 前端配置
FRONTEND:
  port: 3000
  auto_open_browser: false       # 是否自动打开浏览器
  theme: "light"                 # 主题：light/dark
  
# 后端配置  
BACKEND:
  host: "0.0.0.0"
  port: 8000
  reload: true                   # 开发模式自动重载
  
# WebSocket配置
WEBSOCKET:
  ping_interval: 30              # ping间隔（秒）
  ping_timeout: 10               # ping超时（秒）
  max_message_size: 10485760     # 最大消息大小（10MB）

# 文件存储配置
STORAGE:
  screenshots_dir: "screenshots"  # 截图存储目录
  reports_dir: "ux_reports"      # 报告存储目录
  logs_dir: "logs"               # 日志存储目录
  max_file_age_days: 30          # 文件最大保存天数

# 性能优化配置
PERFORMANCE:
  screenshot_quality: 80         # 截图质量（1-100）
  screenshot_max_width: 1080     # 截图最大宽度
  screenshot_max_height: 1920    # 截图最大高度
  compress_screenshots: true     # 是否压缩截图
  
# 安全配置
SECURITY:
  cors_origins: ["http://localhost:3000"]  # 允许的CORS源
  max_connections: 10                      # 最大WebSocket连接数
  
# 实验性功能
EXPERIMENTAL:
  multi_device_support: false     # 多设备支持（实验性）
  voice_commands: false           # 语音命令（实验性）
  gesture_recognition: false      # 手势识别（实验性）
