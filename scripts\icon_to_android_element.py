#!/usr/bin/env python3
"""
工具函数：将icon数据转换为AndroidElement格式
"""
import re

class AndroidElement:
    def __init__(self, uid, bbox, attrib):
        self.uid = uid
        self.bbox = bbox
        self.attrib = attrib
    
    def __repr__(self):
        return f"AndroidElement(uid='{self.uid}', bbox={self.bbox}, attrib='{self.attrib}')"

def get_screen_region(x, y, screen_width, screen_height):
    """根据坐标确定屏幕区域"""
    h_region = "left" if x < screen_width / 3 else "center" if x < screen_width * 2 / 3 else "right"
    v_region = "top" if y < screen_height / 3 else "middle" if y < screen_height * 2 / 3 else "bottom"
    return f"{v_region}_{h_region}"

def convert_to_android_elements(icon_data_text, screen_width=1080, screen_height=1920):
    """
    将icon数据转换为AndroidElement列表（所有识别到的元素都设置为可交互）

    Args:
        icon_data_text (str): icon数据文本，格式如：
            icon 0: {'type': 'text', 'bbox': [0.1, 0.2, 0.3, 0.4], 'interactivity': True, 'content': 'Button'}
        screen_width (int): 屏幕宽度像素，默认1080
        screen_height (int): 屏幕高度像素，默认1920

    Returns:
        List[AndroidElement]: 包含所有识别元素的AndroidElement对象列表，全部设置为可交互
    """
    android_elements = []
    element_count = 0  # 用于生成连续的索引

    # 解析每一行icon数据
    lines = icon_data_text.strip().split('\n')

    for line in lines:
        if line.startswith('icon '):
            try:
                # 提取icon数据
                match = re.match(r'icon (\d+): (.+)', line)
                if match:
                    index = int(match.group(1))
                    data_str = match.group(2)

                    # 解析字典
                    icon_data = eval(data_str)

                    # 处理所有元素，不管原始的interactivity值是什么
                    # 使用连续的计数器而不是原始索引
                    element_count += 1

                    # 转换坐标：相对坐标 -> 绝对像素坐标
                    bbox_relative = icon_data['bbox']
                    x1 = int(bbox_relative[0] * screen_width)
                    y1 = int(bbox_relative[1] * screen_height)
                    x2 = int(bbox_relative[2] * screen_width)
                    y2 = int(bbox_relative[3] * screen_height)
                    bbox_absolute = ((x1, y1), (x2, y2))

                    # 生成UID（基于坐标位置）
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    content = icon_data.get('content', '').strip()
                    content_clean = re.sub(r'[^a-zA-Z0-9_]', '_', content)[:15]

                    # 方案1：基于精确坐标（当前使用）
                    if content_clean:
                        uid = f"{content_clean}_at_{center_x}_{center_y}"
                    else:
                        uid = f"{icon_data['type']}_at_{center_x}_{center_y}"

                    # 方案2：基于屏幕区域（备选方案，取消注释使用）
                    # region = get_screen_region(center_x, center_y, screen_width, screen_height)
                    # if content_clean:
                    #     uid = f"{content_clean}_{region}"
                    # else:
                    #     uid = f"{icon_data['type']}_{region}"
                    # # 如果同区域有重复，添加坐标后缀
                    # uid = f"{uid}_{center_x}_{center_y}"

                    # 所有元素都设为可交互（clickable）
                    attrib = "clickable"

                    # 创建AndroidElement
                    android_elem = AndroidElement(uid, bbox_absolute, attrib)
                    android_elements.append(android_elem)

            except Exception as e:
                print(f"解析失败: {line[:50]}... 错误: {e}")
                continue

    print(f"转换完成：从原始数据中提取了 {element_count} 个元素，全部设置为可交互")
    return android_elements

# 使用示例
if __name__ == "__main__":
    # 示例数据
    sample_data = """icon 0: {'type': 'text', 'bbox': [0.8311258554458618, 0.9487684965133667, 0.9688741564750671, 0.9753694534301758], 'interactivity': False, 'content': '01:41/03:04 iJ =761', 'source': 'box_ocr_content_ocr'}
icon 1: {'type': 'icon', 'bbox': [0.6140251755714417, 0.13913820683956146, 0.9704384803771973, 0.38046830892562866], 'interactivity': True, 'content': 'WE ARE ONE @LORRAINE8LEO KU E# ', 'source': 'box_yolo_content_ocr'}
icon 2: {'type': 'icon', 'bbox': [0.022016573697328568, 0.02449142001569271, 0.19023661315441132, 0.07044591754674911], 'interactivity': True, 'content': 'QQ ', 'source': 'box_yolo_content_ocr'}"""
    
    # 转换
    elements = convert_to_android_elements(sample_data, 1080, 1920)
    
    # 显示结果
    for elem in elements:
        print(elem)
