<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="0" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="1" hint=""><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/launcher" class="android.widget.FrameLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/drag_layer" class="android.widget.FrameLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/scrim_view" class="android.view.View" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="6" hint="" /><node index="1" text="" resource-id="com.google.android.apps.nexuslauncher:id/workspace" class="android.widget.ScrollView" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[28,63][1052,1364]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,63][1024,1336]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/search_container_workspace" class="android.widget.FrameLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,63][1024,284]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/bc_smartspace_view" class="android.widget.FrameLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,63][1024,284]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/smartspace_card_pager" class="androidx.viewpager.widget.ViewPager" package="com.google.android.apps.nexuslauncher" content-desc="At a glance" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[56,63][1024,284]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/base_template_card_with_date" class="android.view.ViewGroup" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,63][1024,284]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/text_group" class="android.view.ViewGroup" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[68,149][1012,199]" drawing-order="1" hint=""><node index="0" text="Thu, Jul 31" resource-id="com.google.android.apps.nexuslauncher:id/date" class="android.widget.TextView" package="com.google.android.apps.nexuslauncher" content-desc="Thu, Jul 31" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[68,149][276,199]" drawing-order="1" hint="" /></node></node></node></node></node></node></node></node><node index="2" text="" resource-id="com.google.android.apps.nexuslauncher:id/accessibility_action_view" class="android.view.View" package="com.google.android.apps.nexuslauncher" content-desc="Home" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,63][1080,1857]" drawing-order="1" hint="" /><node index="3" text="" resource-id="com.google.android.apps.nexuslauncher:id/page_indicator" class="android.view.View" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1398][1080,1461]" drawing-order="4" hint="" /><node index="4" text="" resource-id="com.google.android.apps.nexuslauncher:id/hotseat" class="android.view.ViewGroup" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1461][1080,1920]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[72,1461][1008,1616]" drawing-order="1" hint=""><node index="0" text="Phone" resource-id="" class="android.widget.TextView" package="com.google.android.apps.nexuslauncher" content-desc="Phone" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[72,1461][209,1616]" drawing-order="1" hint="" /><node index="1" text="Messages" resource-id="" class="android.widget.TextView" package="com.google.android.apps.nexuslauncher" content-desc="Messages" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[272,1461][409,1616]" drawing-order="2" hint="" /><node index="2" text="Play Store" resource-id="" class="android.widget.TextView" package="com.google.android.apps.nexuslauncher" content-desc="Play Store has 1 notification" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[472,1461][609,1616]" drawing-order="3" hint="" /><node index="3" text="Chrome" resource-id="" class="android.widget.TextView" package="com.google.android.apps.nexuslauncher" content-desc="Chrome" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[672,1461][809,1616]" drawing-order="4" hint="" /><node index="4" text="Camera" resource-id="" class="android.widget.TextView" package="com.google.android.apps.nexuslauncher" content-desc="Camera" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[872,1461][1008,1616]" drawing-order="5" hint="" /></node><node index="1" text="" resource-id="com.google.android.apps.nexuslauncher:id/search_container_hotseat" class="android.widget.FrameLayout" package="com.google.android.apps.nexuslauncher" content-desc="Google search" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[77,1645][1003,1810]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/g_icon" class="android.widget.ImageView" package="com.google.android.apps.nexuslauncher" content-desc="Google app" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[88,1664][214,1790]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.google.android.apps.nexuslauncher:id/end_part" class="android.widget.LinearLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[740,1645][992,1810]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.google.android.apps.nexuslauncher:id/mic_icon" class="android.widget.ImageView" package="com.google.android.apps.nexuslauncher" content-desc="Voice search" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[740,1645][866,1810]" drawing-order="2" hint="" /><node index="1" text="" resource-id="com.google.android.apps.nexuslauncher:id/lens_icon" class="android.widget.ImageButton" package="com.google.android.apps.nexuslauncher" content-desc="Google Lens" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[866,1645][992,1810]" drawing-order="3" hint="" /></node></node></node><node index="5" text="" resource-id="com.google.android.apps.nexuslauncher:id/overview_actions_view" class="android.widget.FrameLayout" package="com.google.android.apps.nexuslauncher" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1570][1080,1920]" drawing-order="8" hint="" /></node></node></node></node></node></hierarchy>