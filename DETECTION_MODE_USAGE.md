# Self Explorer 检测模式使用说明

## 概述

现在 `self_explorer.py` 支持两种元素检测模式：
- **XML模式**：使用传统的Android XML解析方式
- **视觉模式**：使用YOLO视觉检测方式（默认）

## 使用方法

### 1. 使用视觉模式（默认）
```bash
python scripts/self_explorer.py --app YourApp
# 或者显式指定
python scripts/self_explorer.py --app YourApp --detection_mode visual
```

### 2. 使用XML模式
```bash
python scripts/self_explorer.py --app YourApp --detection_mode xml
```

## 两种模式的区别

### XML模式
- **优点**：
  - 精确的元素边界
  - 包含完整的元素属性信息
  - 不依赖外部服务
  - 处理速度快
- **缺点**：
  - 只能检测到有accessibility信息的元素
  - 可能遗漏一些视觉元素
  - 对某些应用支持有限

### 视觉模式
- **优点**：
  - 能检测到所有视觉可见的元素
  - 包括图标、按钮、文本等
  - 更接近人类的视觉感知
  - 支持OCR文本识别
- **缺点**：
  - 需要运行YOLO服务（http://127.0.0.1:7861/）
  - 处理时间较长
  - 边界框可能不够精确

## 运行时输出

### XML模式
```
Using XML detection mode
XML mode: Found 15 elements
```

### 视觉模式
```
Using visual detection mode (YOLO)
Visual mode: Found 23 elements
```

## 建议使用场景

- **XML模式**：适合标准Android应用，需要快速处理
- **视觉模式**：适合复杂界面、游戏应用，或需要检测更多视觉元素的场景

## 注意事项

1. 使用视觉模式前，确保YOLO服务正在运行：`http://127.0.0.1:7861/`
2. 两种模式生成的元素ID格式不同：
   - XML模式：基于元素属性的ID
   - 视觉模式：基于坐标位置的ID（如 `QQ_at_114_91`）
3. 可以根据具体应用和需求选择合适的模式
