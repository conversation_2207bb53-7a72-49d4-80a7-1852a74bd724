"""
UX评估系统测试脚本
用于测试和验证UX评估功能的正确性
"""

import os
import sys
import json
import time
from typing import Dict, Any

# 添加脚本目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from config import load_config
    from model import OpenAIModel, QwenModel
    CONFIG_AVAILABLE = True
except ImportError:
    print("Warning: Config and model modules not available, skipping real model tests")
    CONFIG_AVAILABLE = False

from ux_evaluator import UXEvaluator
from ux_report_generator import UXReportGenerator
from ux_metrics import UXMetrics, ActionType, DecisionType

def print_with_color(text, color):
    """简化的彩色打印函数"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'cyan': '\033[96m',
        'white': '\033[97m'
    }
    reset = '\033[0m'
    print(f"{colors.get(color, '')}{text}{reset}")


class MockAndroidElement:
    """模拟Android元素用于测试"""
    def __init__(self, uid: str, bbox: tuple, attrib: str):
        self.uid = uid
        self.bbox = bbox
        self.attrib = attrib


def create_mock_data():
    """创建模拟测试数据"""
    
    # 模拟元素列表
    mock_elements = [
        MockAndroidElement("button_login", ((100, 200), (300, 250)), "clickable"),
        MockAndroidElement("input_username", ((100, 100), (400, 150)), "focusable"),
        MockAndroidElement("input_password", ((100, 160), (400, 190)), "focusable"),
        MockAndroidElement("link_forgot", ((100, 260), (200, 280)), "clickable"),
    ]
    
    # 模拟交互数据
    mock_interactions = [
        {
            'step': 1,
            'action_type': 'tap',
            'target_element': 'input_username',
            'decision': 'SUCCESS',
            'response_time': 1.2
        },
        {
            'step': 2,
            'action_type': 'text',
            'target_element': 'input_username',
            'decision': 'SUCCESS',
            'response_time': 2.1
        },
        {
            'step': 3,
            'action_type': 'tap',
            'target_element': 'input_password',
            'decision': 'SUCCESS',
            'response_time': 0.8
        },
        {
            'step': 4,
            'action_type': 'text',
            'target_element': 'input_password',
            'decision': 'SUCCESS',
            'response_time': 1.5
        },
        {
            'step': 5,
            'action_type': 'tap',
            'target_element': 'button_login',
            'decision': 'SUCCESS',
            'response_time': 0.9
        },
        {
            'step': 6,
            'action_type': 'FINISH',
            'target_element': None,
            'decision': 'SUCCESS',
            'response_time': 0.0
        }
    ]
    
    return mock_elements, mock_interactions


def test_ux_metrics():
    """测试UX指标计算"""
    print_with_color("Testing UX Metrics calculation...", "yellow")
    
    metrics = UXMetrics()
    mock_elements, mock_interactions = create_mock_data()
    
    # 添加模拟交互数据
    for interaction in mock_interactions:
        metrics.add_action_record(
            step=interaction['step'],
            action_type=interaction['action_type'],
            target_element=interaction['target_element'],
            decision=interaction['decision'],
            response_time=interaction['response_time']
        )
    
    # 计算指标
    result = metrics.calculate_comprehensive_metrics()
    
    # 验证结果
    print_with_color(f"Task Completion Rate: {result.task_completion_rate:.2f}", "green")
    print_with_color(f"Total Steps: {result.total_steps}", "green")
    print_with_color(f"Error Rate: {result.error_rate:.2f}", "green")
    print_with_color(f"Navigation Efficiency: {result.navigation_efficiency:.2f}", "green")
    print_with_color(f"Overall UX Score: {result.overall_ux_score:.2f}", "green")
    
    assert result.task_completion_rate == 1.0, "Task should be completed"
    assert result.total_steps == 6, "Should have 6 steps"
    assert result.error_rate == 0.0, "Should have no errors"
    
    print_with_color("✅ UX Metrics test passed!", "green")
    return result


def test_ux_evaluator_without_model():
    """测试UX评估器（不使用实际模型）"""
    print_with_color("Testing UX Evaluator (without model calls)...", "yellow")
    
    # 创建模拟模型
    class MockModel:
        def get_model_response(self, prompt, images):
            # 返回模拟响应
            mock_response = """
            Accessibility Score: 7.5 with good element visibility
            Layout Score: 8.0 with logical organization
            Visual Hierarchy Score: 7.0 with clear hierarchy
            Touch Target Score: 8.5 with appropriate sizes
            Information Density Score: 7.5 with balanced content
            Overall Usability Score: 7.7
            Key Issues: 
            - Some text contrast could be improved
            - Navigation could be more intuitive
            Recommendations:
            - Increase text contrast ratio
            - Add breadcrumb navigation
            """
            return True, mock_response
    
    mock_model = MockModel()
    evaluator = UXEvaluator(mock_model, 1080, 1920)
    
    # 添加模拟交互数据
    mock_elements, mock_interactions = create_mock_data()
    for interaction in mock_interactions:
        evaluator.add_interaction_data(
            step=interaction['step'],
            action_type=interaction['action_type'],
            target_element=interaction['target_element'],
            decision=interaction['decision'],
            screenshot_before="mock_before.png",
            screenshot_after="mock_after.png",
            response_time=interaction['response_time']
        )
    
    # 生成评估报告
    evaluation = evaluator.generate_comprehensive_evaluation("Test login task", "TestApp")
    
    print_with_color(f"Overall UX Score: {evaluation.overall_ux_score:.2f}", "green")
    print_with_color(f"Usability Score: {evaluation.usability_score:.1f}/10", "green")
    print_with_color(f"Critical Issues: {len(evaluation.critical_issues)}", "green")
    print_with_color(f"Recommendations: {len(evaluation.recommendations)}", "green")
    
    print_with_color("✅ UX Evaluator test passed!", "green")
    return evaluation


def test_report_generation():
    """测试报告生成"""
    print_with_color("Testing UX Report Generation...", "yellow")
    
    # 使用之前的测试结果
    evaluation = test_ux_evaluator_without_model()
    
    # 创建报告生成器
    test_output_dir = "test_ux_reports"
    report_generator = UXReportGenerator(test_output_dir)
    
    # 生成报告
    saved_files = report_generator.save_report(
        evaluation, 
        "TestApp", 
        format_type="both",
        include_visualizations=False  # 跳过可视化以避免依赖问题
    )
    
    print_with_color("Generated reports:", "green")
    for format_type, file_path in saved_files.items():
        if format_type != 'visualizations':
            print_with_color(f"  {format_type}: {file_path}", "green")
            assert os.path.exists(file_path), f"Report file should exist: {file_path}"
    
    print_with_color("✅ Report Generation test passed!", "green")
    return saved_files


def test_integration_with_real_model():
    """测试与真实模型的集成（可选）"""
    print_with_color("Testing integration with real model...", "yellow")

    if not CONFIG_AVAILABLE:
        print_with_color("Config not available, skipping real model test", "yellow")
        return

    try:
        configs = load_config()

        # 初始化模型
        if configs["MODEL"] == "OpenAI":
            model = OpenAIModel(
                base_url=configs["OPENAI_API_BASE"],
                api_key=configs["OPENAI_API_KEY"],
                model=configs["OPENAI_API_MODEL"],
                temperature=configs["TEMPERATURE"],
                max_tokens=configs["MAX_TOKENS"]
            )
        elif configs["MODEL"] == "Qwen":
            model = QwenModel(
                api_key=configs["DASHSCOPE_API_KEY"],
                model=configs["QWEN_MODEL"]
            )
        else:
            print_with_color("Unsupported model type, skipping real model test", "yellow")
            return

        # 创建一个简单的测试截图路径（如果存在的话）
        test_screenshot = "test_screenshot.png"
        if not os.path.exists(test_screenshot):
            print_with_color("No test screenshot found, skipping real model test", "yellow")
            return

        evaluator = UXEvaluator(model, 1080, 1920)
        mock_elements, _ = create_mock_data()

        # 进行真实的界面可用性评估
        result = evaluator.evaluate_interface_usability(
            test_screenshot,
            mock_elements,
            "Test interface usability evaluation"
        )

        print_with_color(f"Real model evaluation result: {result}", "green")
        print_with_color("✅ Real model integration test passed!", "green")

    except Exception as e:
        print_with_color(f"Real model test failed (this is expected if no model is configured): {e}", "yellow")


def run_all_tests():
    """运行所有测试"""
    print_with_color("=== Starting UX Evaluator System Tests ===", "blue")
    
    try:
        # 基础功能测试
        test_ux_metrics()
        print()
        
        test_ux_evaluator_without_model()
        print()
        
        test_report_generation()
        print()
        
        # 可选的真实模型测试
        test_integration_with_real_model()
        print()
        
        print_with_color("=== All Tests Completed Successfully! ===", "green")
        print_with_color("UX Evaluation System is ready for use.", "green")
        
    except Exception as e:
        print_with_color(f"Test failed: {e}", "red")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    run_all_tests()
