import argparse
import ast
import datetime
import json
import os
import re
import sys
import time

import prompts
from config import load_config
from and_controller import list_all_devices, AndroidController, traverse_tree
from model import parse_explore_rsp, parse_reflect_rsp, OpenAIModel, QwenModel
from utils import print_with_color, draw_bbox_multi
from ux_evaluator import UXEvaluator
from ux_report_generator import UXReportGenerator
from gradio_client import Client, handle_file
from icon_to_android_element import convert_to_android_elements

def choose_detection_mode():
    """交互式选择检测模式"""
    print_with_color("=" * 60, "yellow")
    print_with_color("🤖 AppAgent Detection Mode Selection", "blue")
    print_with_color("=" * 60, "yellow")
    print()

    print_with_color("Choose from the following detection modes:", "blue")
    print_with_color("1. XML Mode - Traditional Android XML parsing", "cyan")
    print_with_color("   ✓ Fast and precise element boundaries", "green")
    print_with_color("   ✓ Complete element attribute information", "green")
    print_with_color("   ✗ May miss some visual elements", "red")
    print()

    print_with_color("2. Visual Mode - YOLO-based visual detection", "cyan")
    print_with_color("   ✓ Detects all visually visible elements", "green")
    print_with_color("   ✓ Includes icons, buttons, text with OCR", "green")
    print_with_color("   ✗ Requires YOLO service running", "red")
    print()

    while True:
        print_with_color("Type 1 or 2 to select detection mode:", "blue")
        choice = input().strip()

        if choice == "1":
            print_with_color("✅ XML Mode selected!", "green")
            return "xml"
        elif choice == "2":
            print_with_color("✅ Visual Mode selected!", "green")
            return "visual"
        else:
            print_with_color("❌ Invalid choice. Please type 1 or 2.", "red")

arg_desc = "AppAgent - Autonomous Exploration"
parser = argparse.ArgumentParser(formatter_class=argparse.RawDescriptionHelpFormatter, description=arg_desc)
parser.add_argument("--app")
parser.add_argument("--root_dir", default="./")
parser.add_argument("--detection_mode", choices=["xml", "visual"], default=None,
                    help="Choose detection mode: 'xml' for XML parsing or 'visual' for YOLO-based detection")
args = vars(parser.parse_args())

# 检测模式选择：命令行参数优先，否则使用交互式选择
detection_mode = args["detection_mode"]
if detection_mode is None:
    detection_mode = choose_detection_mode()

# 根据选择的模式初始化相应的客户端
if detection_mode == "visual":
    client = Client("http://127.0.0.1:7861/")
    print_with_color(f"🔍 Using visual detection mode (YOLO)", "green")
else:
    client = None
    print_with_color(f"📋 Using XML detection mode", "green")

configs = load_config()

if configs["MODEL"] == "OpenAI":
    mllm = OpenAIModel(base_url=configs["OPENAI_API_BASE"],
                       api_key=configs["OPENAI_API_KEY"],
                       model=configs["OPENAI_API_MODEL"],
                       temperature=configs["TEMPERATURE"],
                       max_tokens=configs["MAX_TOKENS"])
elif configs["MODEL"] == "Qwen":
    mllm = QwenModel(api_key=configs["DASHSCOPE_API_KEY"],
                     model=configs["QWEN_MODEL"])
else:
    print_with_color(f"ERROR: Unsupported model type {configs['MODEL']}!", "red")
    sys.exit()

app = args["app"]
root_dir = args["root_dir"]

if not app:
    print_with_color("What is the name of the target app?", "blue")
    app = input()
    app = app.replace(" ", "")

work_dir = os.path.join(root_dir, "apps")
if not os.path.exists(work_dir):
    os.mkdir(work_dir)
work_dir = os.path.join(work_dir, app)
if not os.path.exists(work_dir):
    os.mkdir(work_dir)
demo_dir = os.path.join(work_dir, "demos")
if not os.path.exists(demo_dir):
    os.mkdir(demo_dir)
demo_timestamp = int(time.time())
task_name = datetime.datetime.fromtimestamp(demo_timestamp).strftime("self_explore_%Y-%m-%d_%H-%M-%S")
task_dir = os.path.join(demo_dir, task_name)
os.mkdir(task_dir)
docs_dir = os.path.join(work_dir, "auto_docs")
if not os.path.exists(docs_dir):
    os.mkdir(docs_dir)
explore_log_path = os.path.join(task_dir, f"log_explore_{task_name}.txt")
reflect_log_path = os.path.join(task_dir, f"log_reflect_{task_name}.txt")

# 创建UX评估相关目录和对象
ux_reports_dir = os.path.join(work_dir, "ux_reports")
if not os.path.exists(ux_reports_dir):
    os.mkdir(ux_reports_dir)

device_list = list_all_devices()
if not device_list:
    print_with_color("ERROR: No device found!", "red")
    sys.exit()
print_with_color(f"List of devices attached:\n{str(device_list)}", "yellow")
if len(device_list) == 1:
    device = device_list[0]
    print_with_color(f"Device selected: {device}", "yellow")
else:
    print_with_color("Please choose the Android device to start demo by entering its ID:", "blue")
    device = input()
controller = AndroidController(device)
width, height = controller.get_device_size()
if not width and not height:
    print_with_color("ERROR: Invalid device size!", "red")
    sys.exit()
print_with_color(f"Screen resolution of {device}: {width}x{height}", "yellow")

# 初始化UX评估器
ux_evaluator = UXEvaluator(mllm, width, height)
ux_report_generator = UXReportGenerator(ux_reports_dir)

print_with_color("Please enter the description of the task you want me to complete in a few sentences:", "blue")
task_desc = input()

round_count = 0
doc_count = 0
useless_list = set()
last_act = "None"
task_complete = False
while round_count < configs["MAX_ROUNDS"]:
    round_count += 1
    print_with_color(f"Round {round_count}", "yellow")
    screenshot_before = controller.get_screenshot(f"{round_count}_before", task_dir)

    if detection_mode == "xml":
        # XML模式：使用传统的XML解析方式
        xml_path = controller.get_xml(f"{round_count}", task_dir)
        if screenshot_before == "ERROR" or xml_path == "ERROR":
            break
        clickable_list = []
        focusable_list = []
        traverse_tree(xml_path, clickable_list, "clickable", True)
        traverse_tree(xml_path, focusable_list, "focusable", True)
        elem_list = []
        for elem in clickable_list:
            if elem.uid in useless_list:
                continue
            elem_list.append(elem)
        for elem in focusable_list:
            if elem.uid in useless_list:
                continue
            bbox = elem.bbox
            center = (bbox[0][0] + bbox[1][0]) // 2, (bbox[0][1] + bbox[1][1]) // 2
            close = False
            for e in clickable_list:
                bbox = e.bbox
                center_ = (bbox[0][0] + bbox[1][0]) // 2, (bbox[0][1] + bbox[1][1]) // 2
                dist = (abs(center[0] - center_[0]) ** 2 + abs(center[1] - center_[1]) ** 2) ** 0.5
                if dist <= configs["MIN_DIST"]:
                    close = True
                    break
            if not close:
                elem_list.append(elem)
        print_with_color(f"XML mode: Found {len(elem_list)} elements", "cyan")
    else:
        # 视觉模式：使用YOLO检测方式
        if screenshot_before == "ERROR":
            break
        elem_temp = client.predict(
            image_input=handle_file(screenshot_before),
            box_threshold=0.05,
            iou_threshold=0.1,
            use_paddleocr=True,
            imgsz=640,
            api_name="/process"
        )
        elem_yolo = elem_temp[1]
        elem_list = convert_to_android_elements(elem_yolo, screen_width=width, screen_height=height)
        print_with_color(f"Visual mode: Found {len(elem_list)} elements", "cyan")

    draw_bbox_multi(screenshot_before, os.path.join(task_dir, f"{round_count}_before_labeled.png"), elem_list,
                    dark_mode=configs["DARK_MODE"])

    # UX评估：界面可用性分析（第1轮和每3轮进行一次，确保短任务也能评估）
    if round_count == 1 or round_count % 3 == 0:
        print_with_color("Performing UX interface usability evaluation...", "cyan")
        try:
            interface_analysis = ux_evaluator.evaluate_interface_usability(
                os.path.join(task_dir, f"{round_count}_before_labeled.png"),
                elem_list,
                task_desc
            )
            print_with_color(f"Interface usability score: {interface_analysis.get('overall_score', 'N/A')}/10", "cyan")
        except Exception as e:
            print_with_color(f"UX interface evaluation failed: {e}", "red")

    prompt = re.sub(r"<task_description>", task_desc, prompts.self_explore_task_template)
    prompt = re.sub(r"<last_act>", last_act, prompt)
    base64_img_before = os.path.join(task_dir, f"{round_count}_before_labeled.png")
    print_with_color("Thinking about what to do in the next step...", "yellow")
    status, rsp = mllm.get_model_response(prompt, [base64_img_before])

    if status:
        with open(explore_log_path, "a") as logfile:
            log_item = {"step": round_count, "prompt": prompt, "image": f"{round_count}_before_labeled.png",
                        "response": rsp}
            logfile.write(json.dumps(log_item) + "\n")
        res = parse_explore_rsp(rsp)
        act_name = res[0]
        last_act = res[-1]
        res = res[:-1]

        # 调试信息：显示当前可用元素数量
        print_with_color(f"Available elements: {len(elem_list)}, Action: {act_name}", "cyan")
        if len(res) > 1 and isinstance(res[1], int):
            print_with_color(f"Requested area: {res[1]}", "cyan")

        if act_name == "FINISH":
            task_complete = True
            # 记录FINISH操作到UX评估器
            try:
                ux_evaluator.add_interaction_data(
                    step=round_count,
                    action_type="FINISH",
                    target_element=None,
                    decision="SUCCESS",
                    screenshot_before=os.path.join(task_dir, f"{round_count}_before_labeled.png"),
                    screenshot_after="",
                    response_time=0.0
                )
                print_with_color(f"Task completed! FINISH action recorded at step {round_count}", "green")
            except Exception as e:
                print_with_color(f"Failed to record FINISH action to UX evaluator: {e}", "red")
            break
        if act_name == "tap":
            _, area = res
            tl, br = elem_list[area - 1].bbox
            x, y = (tl[0] + br[0]) // 2, (tl[1] + br[1]) // 2
            ret = controller.tap(x, y)
            if ret == "ERROR":
                print_with_color("ERROR: tap execution failed", "red")
                break
        elif act_name == "text":
            _, input_str = res
            ret = controller.text(input_str)
            if ret == "ERROR":
                print_with_color("ERROR: text execution failed", "red")
                break
        elif act_name == "long_press":
            _, area = res
            tl, br = elem_list[area - 1].bbox
            x, y = (tl[0] + br[0]) // 2, (tl[1] + br[1]) // 2
            ret = controller.long_press(x, y)
            if ret == "ERROR":
                print_with_color("ERROR: long press execution failed", "red")
                break
        elif act_name == "swipe":
            _, area, swipe_dir, dist = res
            tl, br = elem_list[area - 1].bbox
            x, y = (tl[0] + br[0]) // 2, (tl[1] + br[1]) // 2
            ret = controller.swipe(x, y, swipe_dir, dist)
            if ret == "ERROR":
                print_with_color("ERROR: swipe execution failed", "red")
                break
        else:
            break

        # 记录操作到UX评估器（非FINISH操作）
        if act_name != "FINISH":
            try:
                ux_evaluator.add_interaction_data(
                    step=round_count,
                    action_type=act_name,
                    target_element=elem_list[area - 1].uid if act_name in ["tap", "long_press"] and area <= len(elem_list) else None,
                    decision="CONTINUE",  # 暂时标记为继续，后续会在反思阶段更新
                    screenshot_before=os.path.join(task_dir, f"{round_count}_before_labeled.png"),
                    screenshot_after="",  # 将在获取after截图后更新
                    response_time=configs["REQUEST_INTERVAL"]
                )
            except Exception as e:
                print_with_color(f"Failed to record UX interaction data: {e}", "red")

        time.sleep(configs["REQUEST_INTERVAL"])
    else:
        print_with_color(rsp, "red")
        break

    screenshot_after = controller.get_screenshot(f"{round_count}_after", task_dir)
    if screenshot_after == "ERROR":
        break
    draw_bbox_multi(screenshot_after, os.path.join(task_dir, f"{round_count}_after_labeled.png"), elem_list,
                    dark_mode=configs["DARK_MODE"])
    base64_img_after = os.path.join(task_dir, f"{round_count}_after_labeled.png")

    if act_name == "tap":
        prompt = re.sub(r"<action>", "tapping", prompts.self_explore_reflect_template)
    elif act_name == "text":
        continue
    elif act_name == "long_press":
        prompt = re.sub(r"<action>", "long pressing", prompts.self_explore_reflect_template)
    elif act_name == "swipe":
        swipe_dir = res[2]
        if swipe_dir == "up" or swipe_dir == "down":
            act_name = "v_swipe"
        elif swipe_dir == "left" or swipe_dir == "right":
            act_name = "h_swipe"
        prompt = re.sub(r"<action>", "swiping", prompts.self_explore_reflect_template)
    else:
        print_with_color("ERROR: Undefined act!", "red")
        break
    prompt = re.sub(r"<ui_element>", str(area), prompt)
    prompt = re.sub(r"<task_desc>", task_desc, prompt)
    prompt = re.sub(r"<last_act>", last_act, prompt)

    print_with_color("Reflecting on my previous action...", "yellow")
    status, rsp = mllm.get_model_response(prompt, [base64_img_before, base64_img_after])
    if status:
        resource_id = elem_list[int(area) - 1].uid
        with open(reflect_log_path, "a") as logfile:
            log_item = {"step": round_count, "prompt": prompt, "image_before": f"{round_count}_before_labeled.png",
                        "image_after": f"{round_count}_after.png", "response": rsp}
            logfile.write(json.dumps(log_item) + "\n")
        res = parse_reflect_rsp(rsp)
        decision = res[0]
        if decision == "ERROR":
            break

        # 清理decision字符串，防止空格等字符导致匹配失败
        decision = decision.strip().upper()

        # 更新UX评估器中的决策状态
        try:
            # 找到当前步骤的交互记录并更新决策
            if ux_evaluator.evaluation_history:
                for interaction in reversed(ux_evaluator.evaluation_history):
                    if interaction['step'] == round_count:
                        interaction['decision'] = decision
                        interaction['screenshot_after'] = base64_img_after
                        print_with_color(f"Updated UX interaction decision for step {round_count}: {decision}", "cyan")
                        break
        except Exception as e:
            print_with_color(f"Failed to update UX interaction decision: {e}", "red")

        # UX评估：交互效率分析
        if round_count > 1:
            try:
                print_with_color("Evaluating interaction efficiency...", "cyan")
                previous_actions = f"Step {round_count-1}: {last_act}" if last_act != "None" else "Initial action"
                efficiency_analysis = ux_evaluator.evaluate_interaction_efficiency(
                    base64_img_before,
                    base64_img_after,
                    act_name,
                    resource_id,
                    task_desc,
                    round_count,
                    previous_actions
                )
                print_with_color(f"Interaction efficiency score: {efficiency_analysis.get('overall_score', 'N/A')}/10", "cyan")
            except Exception as e:
                print_with_color(f"UX interaction evaluation failed: {e}", "red")

        if decision == "INEFFECTIVE":
            useless_list.add(resource_id)
            last_act = "None"
        elif decision in ["BACK", "CONTINUE", "SUCCESS"]:
            if decision in ["BACK", "CONTINUE"]:
                useless_list.add(resource_id)
                last_act = "None"
                if decision == "BACK":
                    ret = controller.back()
                    if ret == "ERROR":
                        print_with_color("ERROR: back execution failed", "red")
                        break
            doc = res[-1]
            doc_name = resource_id + ".txt"
            doc_path = os.path.join(docs_dir, doc_name)
            if os.path.exists(doc_path):
                doc_content = ast.literal_eval(open(doc_path).read())
                if doc_content[act_name]:
                    print_with_color(f"Documentation for the element {resource_id} already exists.", "yellow")
                    continue
            else:
                doc_content = {
                    "tap": "",
                    "text": "",
                    "v_swipe": "",
                    "h_swipe": "",
                    "long_press": ""
                }
            doc_content[act_name] = doc
            with open(doc_path, "w") as outfile:
                outfile.write(str(doc_content))
            doc_count += 1
            print_with_color(f"Documentation generated and saved to {doc_path}", "yellow")
        else:
            print_with_color(f"ERROR: Undefined decision! {decision}", "red")
            break
    else:
        print_with_color(rsp["error"]["message"], "red")
        break
    time.sleep(configs["REQUEST_INTERVAL"])

if task_complete:
    print_with_color(f"Autonomous exploration completed successfully. {doc_count} docs generated.", "yellow")
elif round_count == configs["MAX_ROUNDS"]:
    print_with_color(f"Autonomous exploration finished due to reaching max rounds. {doc_count} docs generated.",
                     "yellow")
else:
    print_with_color(f"Autonomous exploration finished unexpectedly. {doc_count} docs generated.", "red")

# 生成综合UX评估报告
print_with_color("Generating comprehensive UX evaluation report...", "yellow")
try:
    ux_evaluation = ux_evaluator.generate_comprehensive_evaluation(task_desc, app)
    saved_reports = ux_report_generator.save_report(ux_evaluation, app, "both")

    print_with_color("=== UX EVALUATION SUMMARY ===", "green")
    print_with_color(f"Overall UX Score: {ux_evaluation.overall_ux_score:.2f}/1.0", "green")
    print_with_color(f"Task Completion Rate: {ux_evaluation.metrics.task_completion_rate:.1%}", "green")
    print_with_color(f"Navigation Efficiency: {ux_evaluation.metrics.navigation_efficiency:.2f}", "green")
    print_with_color(f"Error Rate: {ux_evaluation.metrics.error_rate:.1%}", "green")
    print_with_color(f"User Frustration Index: {ux_evaluation.metrics.user_frustration_index:.2f}", "green")

    if saved_reports:
        print_with_color("UX Reports saved:", "green")
        for format_type, file_path in saved_reports.items():
            print_with_color(f"  {format_type.upper()}: {file_path}", "green")

    # 显示关键问题和建议
    if ux_evaluation.critical_issues:
        print_with_color("Critical UX Issues:", "red")
        for i, issue in enumerate(ux_evaluation.critical_issues[:5], 1):
            print_with_color(f"  {i}. {issue}", "red")

    if ux_evaluation.recommendations:
        print_with_color("Top UX Recommendations:", "blue")
        for i, rec in enumerate(ux_evaluation.recommendations[:5], 1):
            print_with_color(f"  {i}. {rec}", "blue")

except Exception as e:
    print_with_color(f"Failed to generate UX evaluation report: {e}", "red")
