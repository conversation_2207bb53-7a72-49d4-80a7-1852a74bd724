<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[109,303][971,1616]" drawing-order="0" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[109,303][971,1616]" drawing-order="1" hint=""><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[109,303][971,1616]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[109,303][971,1616]" drawing-order="1" hint=""><node index="0" text="Select time" resource-id="com.google.android.deskclock:id/header_title" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[172,345][343,395]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.google.android.deskclock:id/material_timepicker_view" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[172,419][908,1417]" drawing-order="3" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/material_clock_display_and_toggle" class="android.view.View" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[172,419][908,671]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.google.android.deskclock:id/material_clock_display" class="android.widget.LinearLayout" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[172,440][739,650]" drawing-order="2" hint=""><node index="0" text="07" resource-id="com.google.android.deskclock:id/material_hour_tv" class="android.view.View" package="com.google.android.deskclock" content-desc="7 o'clock" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[172,440][424,650]" drawing-order="1" hint="" /><node index="1" text=":" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[424,440][487,650]" drawing-order="2" hint="" /><node index="2" text="00" resource-id="com.google.android.deskclock:id/material_minute_tv" class="android.view.View" package="com.google.android.deskclock" content-desc="0 minutes" checkable="true" checked="true" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[487,440][739,650]" drawing-order="3" hint="" /></node><node index="2" text="" resource-id="com.google.android.deskclock:id/material_clock_period_toggle" class="android.widget.LinearLayout" package="com.google.android.deskclock" content-desc="Select AM or PM" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[771,419][908,671]" drawing-order="3" hint=""><node index="0" text="AM" resource-id="com.google.android.deskclock:id/material_clock_period_am_button" class="android.widget.CompoundButton" package="com.google.android.deskclock" content-desc="" checkable="true" checked="true" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[771,419][908,545]" drawing-order="2" hint="" /><node index="1" text="PM" resource-id="com.google.android.deskclock:id/material_clock_period_pm_button" class="android.widget.CompoundButton" package="com.google.android.deskclock" content-desc="" checkable="true" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[771,542][908,668]" drawing-order="1" hint="" /></node><node index="3" text="" resource-id="com.google.android.deskclock:id/material_clock_face" class="android.view.ViewGroup" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[204,745][876,1417]" drawing-order="4" hint=""><node index="0" text="" resource-id="com.google.android.deskclock:id/material_clock_hand" class="android.view.View" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[204,745][876,1417]" drawing-order="2" hint="" /><node index="1" text="55" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="55 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[346,792][473,918]" drawing-order="14" hint="" /><node index="2" text="00" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="00 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[478,757][604,883]" drawing-order="3" hint="" /><node index="3" text="50" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="50 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[251,887][377,1014]" drawing-order="13" hint="" /><node index="4" text="05" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="05 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[609,792][735,918]" drawing-order="4" hint="" /><node index="5" text="10" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="10 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[704,887][830,1014]" drawing-order="5" hint="" /><node index="6" text="45" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="45 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[216,1018][342,1144]" drawing-order="12" hint="" /><node index="7" text="" resource-id="com.google.android.deskclock:id/circle_center" class="android.view.View" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[540,1081][541,1082]" drawing-order="1" hint="" /><node index="8" text="15" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="15 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[740,1019][866,1145]" drawing-order="6" hint="" /><node index="9" text="40" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="40 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[251,1150][377,1276]" drawing-order="11" hint="" /><node index="10" text="35" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="35 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[347,1245][473,1371]" drawing-order="10" hint="" /><node index="11" text="30" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="30 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[478,1281][604,1407]" drawing-order="9" hint="" /><node index="12" text="25" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="25 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[608,1245][735,1371]" drawing-order="8" hint="" /><node index="13" text="20" resource-id="" class="android.widget.TextView" package="com.google.android.deskclock" content-desc="20 minutes" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[704,1150][830,1276]" drawing-order="7" hint="" /></node></node><node index="2" text="" resource-id="com.google.android.deskclock:id/material_timepicker_mode_button" class="android.widget.Button" package="com.google.android.deskclock" content-desc="Switch to text input mode for the time input." checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[141,1480][268,1606]" drawing-order="5" hint="" /><node index="3" text="Cancel" resource-id="com.google.android.deskclock:id/material_timepicker_cancel_button" class="android.widget.Button" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[572,1485][761,1611]" drawing-order="6" hint="" /><node index="4" text="OK" resource-id="com.google.android.deskclock:id/material_timepicker_ok_button" class="android.widget.Button" package="com.google.android.deskclock" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[782,1485][950,1611]" drawing-order="7" hint="" /></node></node></node></node></hierarchy>