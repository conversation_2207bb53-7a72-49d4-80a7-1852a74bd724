"""
UX评估报告生成器
生成专业的UX评估报告文档
"""

import json
import os
import time
from typing import Dict, List, Any
from datetime import datetime

from ux_evaluator import UXEvaluationResult
from ux_metrics import UXMetricsResult
from ux_visualizer import UXVisualizer


class UXReportGenerator:
    """UX报告生成器"""
    
    def __init__(self, output_dir: str = "ux_reports"):
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 初始化可视化器
        viz_dir = os.path.join(output_dir, "visualizations")
        self.visualizer = UXVisualizer(viz_dir)
    
    def generate_markdown_report(self, evaluation: UXEvaluationResult, 
                               app_name: str = "Unknown App") -> str:
        """生成Markdown格式的UX评估报告"""
        
        report_content = f"""# UX评估报告 - {app_name}

## 📊 评估概览

**评估时间**: {evaluation.timestamp}  
**任务描述**: {evaluation.task_description}  
**综合UX评分**: {evaluation.overall_ux_score:.2f}/1.0  

---

## 🎯 执行摘要

本次UX评估针对 **{app_name}** 进行了全面的用户体验分析。通过对用户交互行为、界面可用性、可访问性等多个维度的深入评估，识别出了关键的UX问题并提供了改进建议。

### 关键发现
- **任务完成率**: {evaluation.metrics.task_completion_rate:.1%}
- **操作效率**: {evaluation.metrics.navigation_efficiency:.2f}
- **用户挫败感**: {evaluation.metrics.user_frustration_index:.2f}
- **界面可用性**: {evaluation.usability_score:.1f}/10
- **可访问性**: {evaluation.accessibility_score:.1f}/10

---

## 📈 量化指标分析

### 基础性能指标
| 指标 | 数值 | 评价 |
|------|------|------|
| 任务完成率 | {evaluation.metrics.task_completion_rate:.1%} | {'✅ 优秀' if evaluation.metrics.task_completion_rate >= 0.9 else '⚠️ 需改进' if evaluation.metrics.task_completion_rate >= 0.7 else '❌ 较差'} |
| 总操作步数 | {evaluation.metrics.total_steps} | {'✅ 高效' if evaluation.metrics.total_steps <= 10 else '⚠️ 一般' if evaluation.metrics.total_steps <= 20 else '❌ 冗长'} |
| 成功操作数 | {evaluation.metrics.successful_steps} | - |
| 错误率 | {evaluation.metrics.error_rate:.1%} | {'✅ 优秀' if evaluation.metrics.error_rate <= 0.1 else '⚠️ 需改进' if evaluation.metrics.error_rate <= 0.3 else '❌ 较高'} |
| 回退率 | {evaluation.metrics.back_rate:.1%} | {'✅ 优秀' if evaluation.metrics.back_rate <= 0.1 else '⚠️ 需改进' if evaluation.metrics.back_rate <= 0.2 else '❌ 较高'} |

### 用户体验指标
| 指标 | 数值 | 说明 |
|------|------|------|
| 导航效率 | {evaluation.metrics.navigation_efficiency:.2f} | 用户达成目标的直接程度 |
| 交互流畅度 | {evaluation.metrics.interaction_smoothness:.2f} | 操作的连贯性和响应性 |
| 用户挫败感 | {evaluation.metrics.user_frustration_index:.2f} | 用户在使用过程中的挫败程度 |
| 目标达成度 | {evaluation.metrics.goal_achievement_score:.2f} | 任务目标的实现程度 |

### 操作类型分布
{self._format_distribution_table(evaluation.metrics.action_distribution)}

### 决策类型分布
{self._format_distribution_table(evaluation.metrics.decision_distribution)}

---

## 🔍 详细分析

### 界面可用性分析
{self._format_interface_analysis(evaluation.interface_analysis)}

### 可访问性分析
{self._format_accessibility_analysis(evaluation.accessibility_analysis)}

### 交互效率分析
{self._format_interaction_analysis(evaluation.interaction_analysis)}

---

## ⚠️ 关键问题

{self._format_issues_list(evaluation.critical_issues)}

---

## 💡 改进建议

{self._format_recommendations_list(evaluation.recommendations)}

---

## 🔄 错误模式分析

{self._format_error_patterns(evaluation.metrics.error_patterns)}

---

## 📋 评估标准

本次评估基于以下UX标准和最佳实践：
- **尼尔森10项可用性启发式原则**
- **网页内容无障碍指南2.1**
- **谷歌材料设计规范**
- **苹果人机界面指南**

---

## 📊 评分说明

- **1-3分**: 存在严重问题，需要立即改进
- **4-6分**: 基本可用，但有明显改进空间
- **7-8分**: 良好的用户体验，有小幅优化空间
- **9-10分**: 优秀的用户体验，符合最佳实践

---

*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*  
*评估工具: AppAgent UX Evaluator v1.0*
"""
        
        return report_content
    
    def generate_json_report(self, evaluation: UXEvaluationResult, 
                           app_name: str = "Unknown App") -> Dict[str, Any]:
        """生成JSON格式的UX评估报告"""
        
        return {
            "report_metadata": {
                "app_name": app_name,
                "evaluation_timestamp": evaluation.timestamp,
                "report_version": "1.0",
                "generator": "AppAgent UX Evaluator"
            },
            "task_info": {
                "description": evaluation.task_description,
                "completion_status": evaluation.metrics.task_completion_rate >= 1.0
            },
            "overall_scores": {
                "comprehensive_ux_score": round(evaluation.overall_ux_score, 3),
                "usability_score": round(evaluation.usability_score, 1),
                "accessibility_score": round(evaluation.accessibility_score, 1),
                "consistency_score": round(evaluation.consistency_score, 1),
                "user_experience_score": round(evaluation.user_experience_score, 1)
            },
            "quantitative_metrics": {
                "task_completion_rate": round(evaluation.metrics.task_completion_rate, 3),
                "total_steps": evaluation.metrics.total_steps,
                "successful_steps": evaluation.metrics.successful_steps,
                "error_rate": round(evaluation.metrics.error_rate, 3),
                "back_rate": round(evaluation.metrics.back_rate, 3),
                "navigation_efficiency": round(evaluation.metrics.navigation_efficiency, 3),
                "user_frustration_index": round(evaluation.metrics.user_frustration_index, 3),
                "interaction_smoothness": round(evaluation.metrics.interaction_smoothness, 3),
                "goal_achievement_score": round(evaluation.metrics.goal_achievement_score, 3),
                "action_distribution": evaluation.metrics.action_distribution,
                "decision_distribution": evaluation.metrics.decision_distribution
            },
            "qualitative_analysis": {
                "interface_analysis": evaluation.interface_analysis,
                "accessibility_analysis": evaluation.accessibility_analysis,
                "interaction_analysis": evaluation.interaction_analysis
            },
            "issues_and_recommendations": {
                "critical_issues": evaluation.critical_issues,
                "recommendations": evaluation.recommendations,
                "error_patterns": evaluation.metrics.error_patterns
            },
            "evaluation_details": {
                "evaluation_criteria": [
                    "尼尔森10项可用性启发式原则",
                    "网页内容无障碍指南2.1",
                    "谷歌材料设计规范",
                    "苹果人机界面指南"
                ],
                "scoring_scale": "1-10评分标准 (1=较差, 10=优秀)"
            }
        }
    
    def save_report(self, evaluation: UXEvaluationResult, app_name: str = "Unknown App",
                   format_type: str = "both", include_visualizations: bool = True) -> Dict[str, str]:
        """保存UX评估报告"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        app_safe_name = app_name.replace(" ", "_").replace("/", "_")
        
        saved_files = {}

        # 生成可视化图表
        visualizations = {}
        if include_visualizations:
            try:
                visualizations = self.visualizer.generate_all_visualizations(evaluation, app_name)
                saved_files['visualizations'] = visualizations
            except Exception as e:
                print(f"Failed to generate visualizations: {e}")

        if format_type in ["markdown", "both"]:
            # 生成Markdown报告
            markdown_content = self.generate_markdown_report(evaluation, app_name)
            markdown_filename = f"ux_report_{app_safe_name}_{timestamp}.md"
            markdown_path = os.path.join(self.output_dir, markdown_filename)
            
            with open(markdown_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            saved_files['markdown'] = markdown_path
        
        if format_type in ["json", "both"]:
            # 生成JSON报告
            json_content = self.generate_json_report(evaluation, app_name)
            json_filename = f"ux_report_{app_safe_name}_{timestamp}.json"
            json_path = os.path.join(self.output_dir, json_filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_content, f, indent=2, ensure_ascii=False)
            
            saved_files['json'] = json_path
        
        return saved_files
    
    def _format_distribution_table(self, distribution: Dict[str, int]) -> str:
        """格式化分布表格"""
        if not distribution:
            return "无数据"
        
        table = "| 类型 | 次数 | 占比 |\n|------|------|------|\n"
        total = sum(distribution.values())
        
        for item_type, count in sorted(distribution.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total * 100) if total > 0 else 0
            table += f"| {item_type} | {count} | {percentage:.1f}% |\n"
        
        return table
    
    def _format_interface_analysis(self, analysis: Dict[str, Any]) -> str:
        """格式化界面分析"""
        if not analysis:
            return "暂无界面分析数据"
        
        content = f"""
**可访问性评分**: {analysis.get('accessibility_score', '暂无数据')}/10
**布局评分**: {analysis.get('layout_score', '暂无数据')}/10
**视觉层次评分**: {analysis.get('visual_hierarchy_score', '暂无数据')}/10
**触摸目标评分**: {analysis.get('touch_target_score', '暂无数据')}/10
**信息密度评分**: {analysis.get('information_density_score', '暂无数据')}/10

**主要问题**:
{self._format_list_items(analysis.get('key_issues', []))}

**改进建议**:
{self._format_list_items(analysis.get('recommendations', []))}
"""
        return content
    
    def _format_accessibility_analysis(self, analysis: Dict[str, Any]) -> str:
        """格式化可访问性分析"""
        if not analysis:
            return "暂无可访问性分析数据"
        
        content = f"""
**视觉对比度**: {analysis.get('visual_contrast', '暂无数据')}
**文本可读性**: {analysis.get('text_readability', '暂无数据')}
**触摸目标大小**: {analysis.get('touch_target_size', '暂无数据')}
**元素标签**: {analysis.get('element_labeling', '暂无数据')}
**导航支持**: {analysis.get('navigation_support', '暂无数据')}

**关键问题**:
{self._format_list_items(analysis.get('critical_issues', []))}

**修复步骤**:
{self._format_list_items(analysis.get('remediation_steps', []))}
"""
        return content
    
    def _format_interaction_analysis(self, analysis: Dict[str, Any]) -> str:
        """格式化交互分析"""
        if not analysis:
            return "暂无交互分析数据"
        
        content = f"""
**操作适当性**: {analysis.get('action_appropriateness', '未知')}
**学习曲线**: {analysis.get('learning_curve', '未知')}
**用户满意度**: {analysis.get('user_satisfaction', '未知')}
**效率评分**: {analysis.get('efficiency_score', '未知')}/10

**分析详情**: {analysis.get('analysis', '暂无详细分析')}

**改进建议**:
{self._format_list_items(analysis.get('recommendations', []))}
"""
        return content
    
    def _format_issues_list(self, issues: List[str]) -> str:
        """格式化问题列表"""
        if not issues:
            return "✅ 未发现关键问题"
        
        formatted_issues = []
        for i, issue in enumerate(issues[:10], 1):  # 限制最多10个问题
            formatted_issues.append(f"{i}. ❌ {issue}")
        
        return "\n".join(formatted_issues)
    
    def _format_recommendations_list(self, recommendations: List[str]) -> str:
        """格式化建议列表"""
        if not recommendations:
            return "暂无具体建议"
        
        formatted_recs = []
        for i, rec in enumerate(recommendations[:10], 1):  # 限制最多10个建议
            formatted_recs.append(f"{i}. 💡 {rec}")
        
        return "\n".join(formatted_recs)
    
    def _format_error_patterns(self, patterns: List[str]) -> str:
        """格式化错误模式"""
        if not patterns:
            return "✅ 未发现明显的错误模式"
        
        formatted_patterns = []
        for pattern in patterns:
            formatted_patterns.append(f"- 🔍 {pattern}")
        
        return "\n".join(formatted_patterns)
    
    def _format_list_items(self, items: List[str]) -> str:
        """格式化列表项"""
        if not items:
            return "- 无"
        
        return "\n".join([f"- {item}" for item in items[:5]])  # 限制最多5个项目
