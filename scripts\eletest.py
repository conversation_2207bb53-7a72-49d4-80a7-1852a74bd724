import argparse
import ast
import datetime
import json
import os
import re
import sys
import time

import prompts
from config import load_config
from and_controller import list_all_devices, AndroidController, traverse_tree
from model import parse_explore_rsp, parse_reflect_rsp, OpenAIModel, QwenModel
from utils import print_with_color, draw_bbox_multi

configs = load_config()


useless_list = set()
clickable_list = []
focusable_list = []
xml_path = "../apps/Settings/demos/self_explore_2025-07-22_17-02-47/1.xml"
traverse_tree(xml_path, clickable_list, "clickable", True)
traverse_tree(xml_path, focusable_list, "focusable", True)
elem_list = []
for elem in clickable_list:
    if elem.uid in useless_list:
        continue
    elem_list.append(elem)
for elem in focusable_list:
    if elem.uid in useless_list:
        continue
    bbox = elem.bbox
    center = (bbox[0][0] + bbox[1][0]) // 2, (bbox[0][1] + bbox[1][1]) // 2
    close = False
    for e in clickable_list:
        bbox = e.bbox
        center_ = (bbox[0][0] + bbox[1][0]) // 2, (bbox[0][1] + bbox[1][1]) // 2
        dist = (abs(center[0] - center_[0]) ** 2 + abs(center[1] - center_[1]) ** 2) ** 0.5
        if dist <= configs["MIN_DIST"]:
            close = True
            break
    if not close:
        elem_list.append(elem)
print(elem_list)